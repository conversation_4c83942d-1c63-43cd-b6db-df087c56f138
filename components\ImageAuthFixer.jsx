// components/ImageAuthFixer.jsx - 图片认证问题修复工具
import React, { useState } from 'react';
import { View, TouchableOpacity, ScrollView, Platform } from 'react-native';
import ThemedText from './ThemedText';
import ThemedTextInput from './ThemedTextInput';
import AuthenticatedImage from './AuthenticatedImage';
import SafeImage from './SafeImage';
import CleanImage from './CleanImage';
import { createAuthenticatedImageUrl, isImageAuthRequired, transformImageUrl, cleanImageUrl } from '../lib/imageUtils';
import { checkLoginStatus } from '../lib/authUtils';

const ImageAuthFixer = () => {
  const [testImageUrl, setTestImageUrl] = useState('http://localhost:8080/images/tag_10.png');
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [loginStatus, setLoginStatus] = useState(null);

  // 测试图片认证
  const testImageAuth = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      console.log(`[${Platform.OS}] 开始测试图片认证`);
      
      // 1. 检查登录状态
      const status = await checkLoginStatus();
      setLoginStatus(status);
      
      // 2. 测试URL处理
      const cleanUrl = cleanImageUrl(testImageUrl);
      const transformedUrl = transformImageUrl(testImageUrl);
      const needsAuth = isImageAuthRequired(testImageUrl);
      const authUrl = needsAuth ? await createAuthenticatedImageUrl(testImageUrl) : transformedUrl;

      setTestResults({
        originalUrl: testImageUrl,
        cleanUrl: cleanUrl,
        transformedUrl: transformedUrl,
        needsAuth: needsAuth,
        authUrl: authUrl,
        hasToken: !!status.token,
        loginStatus: status.isLoggedIn
      });
      
      console.log(`[${Platform.OS}] 图片认证测试完成:`, {
        needsAuth,
        hasToken: !!status.token,
        authUrlLength: authUrl?.length
      });
      
    } catch (error) {
      console.error(`[${Platform.OS}] 图片认证测试失败:`, error);
      setTestResults({
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  // 常见的测试图片URL
  const commonTestUrls = [
    'http://localhost:8080/images/tag_10.png',
    'http://localhost:8080/images/tag_13.png',
    'http://localhost:8080/images/tag_35.png',
    'http://localhost:8080/images/tag_2.png',
    'http://localhost:8080/images/tag_41.png',
    'http://localhost:8080/images/tag_5.png'
  ];

  return (
    <ScrollView style={{ padding: 20, backgroundColor: '#f8f9fa' }}>
      <ThemedText style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        图片认证修复工具
      </ThemedText>

      {/* 测试URL输入 */}
      <View style={{ marginBottom: 20 }}>
        <ThemedText style={{ marginBottom: 5 }}>测试图片URL:</ThemedText>
        <ThemedTextInput
          value={testImageUrl}
          onChangeText={setTestImageUrl}
          placeholder="输入图片URL"
          style={{ marginBottom: 10 }}
        />
        
        {/* 快速选择常见URL */}
        <ThemedText style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
          快速选择:
        </ThemedText>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
          {commonTestUrls.map((url, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => setTestImageUrl(url)}
              style={{
                backgroundColor: '#e9ecef',
                padding: 5,
                borderRadius: 3,
                margin: 2
              }}
            >
              <ThemedText style={{ fontSize: 10 }}>
                tag_{url.match(/tag_?(\d+)/)?.[1] || index + 1}.png
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* 测试按钮 */}
      <TouchableOpacity
        onPress={testImageAuth}
        disabled={loading}
        style={[
          {
            backgroundColor: loading ? '#6c757d' : '#007bff',
            padding: 15,
            borderRadius: 8,
            alignItems: 'center',
            marginBottom: 20
          }
        ]}
      >
        <ThemedText style={{ color: 'white', fontWeight: 'bold' }}>
          {loading ? '测试中...' : '测试图片认证'}
        </ThemedText>
      </TouchableOpacity>

      {/* 登录状态显示 */}
      {loginStatus && (
        <View style={{
          backgroundColor: loginStatus.isLoggedIn ? '#d4edda' : '#f8d7da',
          padding: 12,
          borderRadius: 5,
          marginBottom: 15,
          borderLeftWidth: 4,
          borderLeftColor: loginStatus.isLoggedIn ? '#28a745' : '#dc3545'
        }}>
          <ThemedText style={{
            color: loginStatus.isLoggedIn ? '#155724' : '#721c24',
            fontWeight: 'bold'
          }}>
            登录状态: {loginStatus.isLoggedIn ? '已登录' : '未登录'}
          </ThemedText>
          <ThemedText style={{
            color: loginStatus.isLoggedIn ? '#155724' : '#721c24',
            fontSize: 12
          }}>
            Token: {loginStatus.token ? '存在' : '不存在'}
          </ThemedText>
        </View>
      )}

      {/* 测试结果 */}
      {testResults && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 15,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#dee2e6',
          marginBottom: 20
        }}>
          <ThemedText style={{ fontWeight: 'bold', marginBottom: 10 }}>
            测试结果:
          </ThemedText>
          
          {testResults.error ? (
            <ThemedText style={{ color: '#dc3545' }}>
              错误: {testResults.error}
            </ThemedText>
          ) : (
            <View>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                原始URL: {testResults.originalUrl}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                清理后URL: {testResults.cleanUrl}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                转换后URL: {testResults.transformedUrl}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                需要认证: {testResults.needsAuth ? '是' : '否'} (后端已放行/images/*)
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                有Token: {testResults.hasToken ? '是' : '否'}
              </ThemedText>
              {testResults.authUrl && (
                <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                  最终URL: {testResults.authUrl.substring(0, 80)}...
                </ThemedText>
              )}
            </View>
          )}
        </View>
      )}

      {/* 图片对比测试 */}
      {testResults && !testResults.error && (
        <View style={{ marginBottom: 20 }}>
          <ThemedText style={{ fontWeight: 'bold', marginBottom: 10 }}>
            图片加载对比:
          </ThemedText>
          
          <View style={{ flexDirection: 'column', gap: 15 }}>
            {/* 普通SafeImage */}
            <View>
              <ThemedText style={{ fontSize: 12, marginBottom: 5, textAlign: 'center' }}>
                普通SafeImage (可能有URL格式问题)
              </ThemedText>
              <SafeImage
                source={testImageUrl}
                style={{ width: '100%', height: 100, backgroundColor: '#f0f0f0' }}
                showDebugInfo={true}
              />
            </View>

            {/* CleanImage */}
            <View>
              <ThemedText style={{ fontSize: 12, marginBottom: 5, textAlign: 'center' }}>
                CleanImage (修复URL格式)
              </ThemedText>
              <CleanImage
                source={testImageUrl}
                style={{ width: '100%', height: 100, backgroundColor: '#f0f0f0' }}
                showDebugInfo={true}
              />
            </View>

            {/* 认证AuthenticatedImage */}
            <View>
              <ThemedText style={{ fontSize: 12, marginBottom: 5, textAlign: 'center' }}>
                AuthenticatedImage (带认证，但/images/已放行)
              </ThemedText>
              <AuthenticatedImage
                source={testImageUrl}
                style={{ width: '100%', height: 100, backgroundColor: '#f0f0f0' }}
                showDebugInfo={true}
                requireAuth={false}
              />
            </View>
          </View>
        </View>
      )}

      {/* 使用说明 */}
      <View style={{
        backgroundColor: '#e9ecef',
        padding: 15,
        borderRadius: 5
      }}>
        <ThemedText style={{ fontSize: 12, color: '#6c757d' }}>
          💡 使用说明：
        </ThemedText>
        <ThemedText style={{ fontSize: 11, color: '#6c757d', marginTop: 5 }}>
          • 输入图片URL进行认证测试{'\n'}
          • 对比普通图片组件和认证图片组件的加载效果{'\n'}
          • 如果看到401错误，说明图片需要认证{'\n'}
          • 使用AuthenticatedImage组件替代SafeImage来解决认证问题
        </ThemedText>
      </View>
    </ScrollView>
  );
};

export default ImageAuthFixer;
