// examples/ApiUsageExample.jsx - 新API系统使用示例
import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, Alert } from 'react-native';
import ThemedText from '../components/ThemedText';
import apiServices from '../lib/apiServices';
import { debugNetworkConfig } from '../lib/apiConfig';

const ApiUsageExample = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [user, setUser] = useState(null);

  // 示例1：获取首页数据
  const fetchIndexData = async () => {
    setLoading(true);
    try {
      const result = await apiServices.content.getIndexData(1);
      setData(result);
      Alert.alert('成功', '首页数据获取成功');
    } catch (error) {
      Alert.alert('错误', `获取数据失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 示例2：用户认证
  const handleLogin = async () => {
    setLoading(true);
    try {
      const result = await apiServices.auth.login('testuser', 'password123');
      setUser(result);
      Alert.alert('成功', '登录成功');
    } catch (error) {
      Alert.alert('错误', `登录失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 示例3：获取用户资料
  const fetchUserProfile = async () => {
    setLoading(true);
    try {
      const profile = await apiServices.profile.getUserProfile();
      Alert.alert('用户资料', JSON.stringify(profile, null, 2));
    } catch (error) {
      Alert.alert('错误', `获取资料失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 示例4：保存用户标签
  const saveUserTags = async () => {
    setLoading(true);
    try {
      const selections = {
        step1: [1, 2, 3],
        step2: [4, 5],
        step3: [6]
      };
      const result = await apiServices.profile.saveUserTagAndGenerateProfile(selections);
      Alert.alert('成功', 'AI画像生成成功');
    } catch (error) {
      Alert.alert('错误', `保存失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 示例5：获取标签列表
  const fetchTags = async () => {
    setLoading(true);
    try {
      const tags = await apiServices.tag.getTagsByStepId(1);
      Alert.alert('标签列表', `获取到 ${tags.length} 个标签`);
    } catch (error) {
      Alert.alert('错误', `获取标签失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 示例6：调试网络配置
  const showNetworkConfig = async () => {
    try {
      const config = await debugNetworkConfig();
      Alert.alert('网络配置', JSON.stringify(config, null, 2));
    } catch (error) {
      Alert.alert('错误', `获取配置失败: ${error.message}`);
    }
  };

  // 示例7：健康检查
  const healthCheck = async () => {
    setLoading(true);
    try {
      const result = await apiServices.common.healthCheck();
      Alert.alert('健康检查', '服务器状态正常');
    } catch (error) {
      Alert.alert('错误', `健康检查失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ padding: 20 }}>
      <ThemedText style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        新API系统使用示例
      </ThemedText>

      {/* 内容API示例 */}
      <TouchableOpacity
        onPress={fetchIndexData}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          获取首页数据
        </ThemedText>
      </TouchableOpacity>

      {/* 认证API示例 */}
      <TouchableOpacity
        onPress={handleLogin}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          用户登录
        </ThemedText>
      </TouchableOpacity>

      {/* 用户资料API示例 */}
      <TouchableOpacity
        onPress={fetchUserProfile}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          获取用户资料
        </ThemedText>
      </TouchableOpacity>

      {/* 标签API示例 */}
      <TouchableOpacity
        onPress={saveUserTags}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          保存用户标签
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={fetchTags}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          获取标签列表
        </ThemedText>
      </TouchableOpacity>

      {/* 工具API示例 */}
      <TouchableOpacity
        onPress={showNetworkConfig}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          查看网络配置
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={healthCheck}
        disabled={loading}
        style={buttonStyle}
      >
        <ThemedText style={buttonTextStyle}>
          健康检查
        </ThemedText>
      </TouchableOpacity>

      {/* 状态显示 */}
      {loading && (
        <ThemedText style={{ marginTop: 20, textAlign: 'center' }}>
          加载中...
        </ThemedText>
      )}

      {data && (
        <View style={{ marginTop: 20 }}>
          <ThemedText style={{ fontWeight: 'bold' }}>数据预览:</ThemedText>
          <ThemedText style={{ fontSize: 12, marginTop: 5 }}>
            {JSON.stringify(data, null, 2).substring(0, 200)}...
          </ThemedText>
        </View>
      )}
    </View>
  );
};

const buttonStyle = {
  backgroundColor: '#007AFF',
  padding: 12,
  borderRadius: 8,
  marginBottom: 10,
  alignItems: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold'
};

export default ApiUsageExample;
