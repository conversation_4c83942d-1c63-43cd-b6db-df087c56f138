// components/GuestModeTest.jsx - 游客模式测试组件
import React, { useState } from 'react';
import { View, TouchableOpacity, ScrollView, Platform } from 'react-native';
import ThemedText from './ThemedText';
import apiServices from '../lib/apiServices';

const GuestModeTest = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  // 测试游客模式首页数据
  const testGuestIndexData = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试游客模式首页数据`);
      
      const data = await apiServices.content.getIndexData(null); // 传入null表示游客模式
      
      console.log(`[${Platform.OS}] 游客模式首页数据测试成功:`, data);
      setResult({
        type: '游客模式首页数据',
        data: data
      });
      
    } catch (err) {
      console.error(`[${Platform.OS}] 游客模式首页数据测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试游客模式推荐视频
  const testGuestRecommendVideos = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试游客模式推荐视频`);
      
      const data = await apiServices.content.getRecommendVideoList(null); // 传入null表示游客模式
      
      console.log(`[${Platform.OS}] 游客模式推荐视频测试成功:`, data);
      setResult({
        type: '游客模式推荐视频',
        data: data
      });
      
    } catch (err) {
      console.error(`[${Platform.OS}] 游客模式推荐视频测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试游客模式推荐内容
  const testGuestRecommendations = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试游客模式推荐内容`);
      
      const data = await apiServices.content.getRecommendations(null); // 传入null表示游客模式
      
      console.log(`[${Platform.OS}] 游客模式推荐内容测试成功:`, data);
      setResult({
        type: '游客模式推荐内容',
        data: data
      });
      
    } catch (err) {
      console.error(`[${Platform.OS}] 游客模式推荐内容测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试网络连接
  const testNetworkConnection = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试网络连接`);
      
      const isConnected = await apiServices.auth.testNetworkConnection();
      
      console.log(`[${Platform.OS}] 网络连接测试结果:`, isConnected);
      setResult({
        type: '网络连接测试',
        data: { connected: isConnected, message: isConnected ? '连接正常' : '连接失败' }
      });
      
    } catch (err) {
      console.error(`[${Platform.OS}] 网络连接测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const renderDataPreview = (data) => {
    if (!data) return null;

    const preview = JSON.stringify(data, null, 2);
    const truncated = preview.length > 500 ? preview.substring(0, 500) + '...' : preview;

    return (
      <View style={{
        backgroundColor: '#f5f5f5',
        padding: 10,
        borderRadius: 5,
        marginTop: 10
      }}>
        <ThemedText style={{ fontSize: 12, fontFamily: 'monospace' }}>
          {truncated}
        </ThemedText>
      </View>
    );
  };

  return (
    <ScrollView style={{ padding: 20 }}>
      <ThemedText style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        游客模式功能测试
      </ThemedText>

      {/* 测试按钮 */}
      <TouchableOpacity
        onPress={testGuestIndexData}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#007AFF' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试游客模式首页数据
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={testGuestRecommendVideos}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#34C759' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试游客模式推荐视频
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={testGuestRecommendations}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#FF9500' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试游客模式推荐内容
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={testNetworkConnection}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#FF3B30' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试网络连接
        </ThemedText>
      </TouchableOpacity>

      {/* 状态显示 */}
      {loading && (
        <View style={{ marginTop: 20, alignItems: 'center' }}>
          <ThemedText style={{ color: '#666' }}>测试中...</ThemedText>
        </View>
      )}

      {error && (
        <View style={{
          backgroundColor: '#ffebee',
          padding: 15,
          borderRadius: 5,
          marginTop: 20,
          borderLeftWidth: 4,
          borderLeftColor: '#f44336'
        }}>
          <ThemedText style={{ color: '#d32f2f', fontWeight: 'bold' }}>
            错误:
          </ThemedText>
          <ThemedText style={{ color: '#d32f2f', marginTop: 5 }}>
            {error}
          </ThemedText>
        </View>
      )}

      {result && (
        <View style={{
          backgroundColor: '#e8f5e8',
          padding: 15,
          borderRadius: 5,
          marginTop: 20,
          borderLeftWidth: 4,
          borderLeftColor: '#4caf50'
        }}>
          <ThemedText style={{ color: '#2e7d32', fontWeight: 'bold' }}>
            测试成功: {result.type}
          </ThemedText>
          
          {/* 数据统计 */}
          {result.data && (
            <View style={{ marginTop: 10 }}>
              <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                数据类型: {Array.isArray(result.data.data) ? '数组' : '对象'}
              </ThemedText>
              {Array.isArray(result.data.data) && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  数组长度: {result.data.data.length}
                </ThemedText>
              )}
              {result.data.data && result.data.data.guestRecommend && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  游客推荐: {result.data.data.guestRecommend.length} 项
                </ThemedText>
              )}
              {result.data.data && result.data.data.featuredContentList && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  精品内容: {result.data.data.featuredContentList.length} 项
                </ThemedText>
              )}
            </View>
          )}
          
          {renderDataPreview(result.data)}
        </View>
      )}
    </ScrollView>
  );
};

const buttonStyle = {
  padding: 15,
  borderRadius: 8,
  marginBottom: 10,
  alignItems: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold',
  fontSize: 14
};

export default GuestModeTest;
