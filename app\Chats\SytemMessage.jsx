import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import { Colors } from '../../constants/Colors';

const SytemMessage = () => {
  const router = useRouter();

  const systemMessages = [
    {
      id: 1,
      type: 'system',
      title: '系统维护通知',
      message: '系统将于今晚23:00-01:00进行维护，期间可能无法正常使用',
      time: '2小时前',
      icon: require('../../assets/favicon.png'),
      isRead: false
    },
    {
      id: 2,
      type: 'update',
      title: '版本更新',
      message: '新版本已发布，包含多项功能优化和bug修复',
      time: '1天前',
      icon: require('../../assets/favicon.png'),
      isRead: true
    },
    {
      id: 3,
      type: 'announcement',
      title: '社区公告',
      message: '欢迎使用学习伙伴AI，让我们一起进步！',
      time: '3天前',
      icon: require('../../assets/favicon.png'),
      isRead: true
    },
    {
      id: 4,
      type: 'security',
      title: '安全提醒',
      message: '请注意保护个人隐私，不要在公共场所泄露账号信息',
      time: '1周前',
      icon: require('../../assets/favicon.png'),
      isRead: true
    }
  ];

  const getMessageTypeColor = (type) => {
    switch (type) {
      case 'system': return '#FF6B6B';
      case 'update': return '#4ECDC4';
      case 'announcement': return '#45B7D1';
      case 'security': return '#FFA726';
      default: return '#999';
    }
  };

  const getMessageTypeText = (type) => {
    switch (type) {
      case 'system': return '系统';
      case 'update': return '更新';
      case 'announcement': return '公告';
      case 'security': return '安全';
      default: return '消息';
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <ThemedView style={styles.container}>
        {/* 安全视图 */}
        <View style={styles.safeAreaCustom} />

        {/* 顶部标题栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => {
            console.log('Navigating back to community');
            if (router.canGoBack()) {
              router.back();
            } else {
              router.push('/(dashboard)/community');
            }
          }}>
            <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>系统消息</ThemedText>
          <TouchableOpacity>
            <Image source={require('../../assets/FrameFour.png')} style={styles.settingsIcon} />
          </TouchableOpacity>
        </View>

        {/* 消息列表 */}
        <ScrollView style={styles.messageList} showsVerticalScrollIndicator={false}>
          {systemMessages.map((message) => (
            <TouchableOpacity key={message.id} style={[styles.messageItem, !message.isRead && styles.unreadMessage]}>
              <View style={styles.messageIcon}>
                <Image source={message.icon} style={styles.iconImage} />
                <View style={[styles.typeBadge, { backgroundColor: getMessageTypeColor(message.type) }]}>
                  <ThemedText style={styles.typeText}>{getMessageTypeText(message.type)}</ThemedText>
                </View>
              </View>
              <View style={styles.messageContent}>
                <View style={styles.messageHeader}>
                  <ThemedText style={[styles.messageTitle, !message.isRead && styles.unreadTitle]}>
                    {message.title}
                  </ThemedText>
                  <ThemedText style={styles.messageTime}>{message.time}</ThemedText>
                </View>
                <ThemedText style={styles.messageText} numberOfLines={2}>
                  {message.message}
                </ThemedText>
              </View>
              {!message.isRead && <View style={styles.unreadDot} />}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeAreaCustom: {
    height: 50,
    backgroundColor: '#fff',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  settingsIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  messageList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  messageItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
  },
  unreadMessage: {
    backgroundColor: '#f8f9ff',
  },
  messageIcon: {
    position: 'relative',
    marginRight: 12,
  },
  iconImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
  },
  typeBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 8,
    minWidth: 20,
  },
  typeText: {
    fontSize: 8,
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  messageContent: {
    flex: 1,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  messageTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.title,
    flex: 1,
    marginRight: 8,
  },
  unreadTitle: {
    fontWeight: 'bold',
  },
  messageTime: {
    fontSize: 12,
    color: '#999',
  },
  messageText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF6B6B',
    marginLeft: 8,
    marginTop: 8,
  },
});

export default SytemMessage;