// constants/index.js
import { Platform } from 'react-native';

// 获取开发机器IP地址的函数
const getDevMachineIP = () => {
  // 常见的开发机器IP地址范围
  // 您需要根据实际情况修改这个IP地址

  // 方法1: 手动设置您的开发机器IP
  // 可以通过以下命令查看：
  // Windows: ipconfig
  // Mac/Linux: ifconfig 或 ip addr show

  // 常见的局域网IP范围：
  // 192.168.x.x (家庭网络)
  // 10.x.x.x (企业网络)
  // 172.16.x.x - 172.31.x.x (企业网络)

  return '*************'; // 请替换为您的实际IP地址
};

// 根据环境和平台动态设置API基础URL
const getApiBaseUrl = () => {
  // 开发环境
  if (__DEV__) {
    // Web端可以使用localhost（通过代理）
    if (Platform.OS === 'web') {
      return 'http://localhost:8080/api';
    }
    // 手机端需要使用开发机器的实际IP地址
    const devIP = getDevMachineIP();
    return `http://${devIP}:8080/api`;
  }
  // 生产环境使用实际部署地址
  return 'https://your-backend-api.com/api';
};

export const API_BASE_URL = getApiBaseUrl();

// 导出IP获取函数，方便调试
export const getDevIP = getDevMachineIP;
