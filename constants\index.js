// constants/index.js
import { Platform } from 'react-native';

// 获取开发机器IP地址的函数
const getDevMachineIP = () => {
  // 使用与 lib/api.js 相同的IP地址配置
  // 根据您的 api.js 文件，您的开发机器IP是 **************

  return '**************'; // 与 lib/api.js 保持一致
};

// 根据环境和平台动态设置API基础URL
const getApiBaseUrl = () => {
  // 开发环境
  if (__DEV__) {
    // Web端可以使用localhost（通过代理）
    if (Platform.OS === 'web') {
      return 'http://localhost:8080/api';
    }
    // 手机端需要使用开发机器的实际IP地址
    const devIP = getDevMachineIP();
    return `http://${devIP}:8080/api`;
  }
  // 生产环境使用实际部署地址
  return 'https://your-backend-api.com/api';
};

export const API_BASE_URL = getApiBaseUrl();

// 导出IP获取函数，方便调试
export const getDevIP = getDevMachineIP;
