// lib/imageUtils.js
import { Platform } from 'react-native';
import { getDevIP } from './apiConfig';

/**
 * 清理和修复图片URL格式
 * @param {string} url - 原始图片URL
 * @returns {string} - 清理后的URL
 */
export const cleanImageUrl = (url) => {
  if (!url) return url;

  let cleanUrl = url.trim();

  // 修复常见的URL格式问题
  cleanUrl = cleanUrl
    .replace(/\s+/g, '_')  // 将空格替换为下划线
    .replace(/_{2,}/g, '_')  // 将多个下划线替换为单个
    .replace(/([^:])\/+/g, '$1/')  // 修复多个斜杠（但保留协议中的://）
    .replace(/\.(png|jpg|jpeg|gif|webp)(\.|_)/gi, '.$1')  // 修复文件扩展名问题
    .replace(/tag(\s+)(\d+)/gi, 'tag_$2');  // 修复 "tag 10" -> "tag_10"

  console.log(`[${Platform.OS}] 图片URL清理:`, {
    original: url,
    cleaned: cleanUrl
  });

  return cleanUrl;
};

/**
 * 转换图片URL，将localhost替换为实际IP地址
 * @param {string} url - 原始图片URL
 * @returns {string} - 转换后的URL
 */
export const transformImageUrl = (url) => {
  if (!url) return url;

  // 首先清理URL格式
  let cleanUrl = cleanImageUrl(url);

  // 对于移动端，需要将localhost替换为实际IP
  if (Platform.OS !== 'web') {
    // 如果URL包含localhost，替换为实际IP
    if (cleanUrl.includes('localhost:8080')) {
      const devIP = getDevIP();
      cleanUrl = cleanUrl.replace('localhost:8080', `${devIP}:8080`);
      console.log(`[${Platform.OS}] 图片URL localhost转换:`, {
        original: url,
        devIP: devIP,
        transformed: cleanUrl
      });
    }

    // 如果URL包含127.0.0.1，也替换为实际IP
    if (cleanUrl.includes('127.0.0.1:8080')) {
      const devIP = getDevIP();
      cleanUrl = cleanUrl.replace('127.0.0.1:8080', `${devIP}:8080`);
      console.log(`[${Platform.OS}] 图片URL 127.0.0.1转换:`, {
        original: url,
        devIP: devIP,
        transformed: cleanUrl
      });
    }
  }

  console.log(`[${Platform.OS}] 图片URL处理完成:`, {
    original: url,
    final: cleanUrl
  });

  return cleanUrl;
};



/**
 * 批量转换对象中的图片URL
 * @param {Object} obj - 包含图片URL的对象
 * @param {string[]} urlFields - 需要转换的URL字段名数组
 * @returns {Object} - 转换后的对象
 */
export const transformObjectImageUrls = (obj, urlFields = ['coverUrl', 'imageUrl', 'avatarUrl', 'thumbnailUrl']) => {
  if (!obj || typeof obj !== 'object') return obj;
  
  const transformed = { ...obj };
  
  urlFields.forEach(field => {
    if (transformed[field]) {
      transformed[field] = transformImageUrl(transformed[field]);
    }
  });
  
  return transformed;
};

/**
 * 批量转换数组中对象的图片URL
 * @param {Array} array - 包含对象的数组
 * @param {string[]} urlFields - 需要转换的URL字段名数组
 * @returns {Array} - 转换后的数组
 */
export const transformArrayImageUrls = (array, urlFields = ['coverUrl', 'imageUrl', 'avatarUrl', 'thumbnailUrl']) => {
  if (!Array.isArray(array)) return array;
  
  return array.map(item => transformObjectImageUrls(item, urlFields));
};

/**
 * 获取安全的图片URL，如果转换失败则返回占位符
 * @param {string} url - 原始图片URL
 * @param {string} placeholder - 占位符URL
 * @returns {string} - 安全的图片URL
 */
export const getSafeImageUrl = (url, placeholder = 'https://via.placeholder.com/300x200?text=No+Image') => {
  try {
    const transformedUrl = transformImageUrl(url);
    return transformedUrl || placeholder;
  } catch (error) {
    console.error(`[${Platform.OS}] 图片URL转换失败:`, error);
    return placeholder;
  }
};

/**
 * 预加载图片，检查图片是否可以正常加载
 * @param {string} url - 图片URL
 * @returns {Promise<boolean>} - 是否加载成功
 */
export const preloadImage = (url) => {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }
    
    if (Platform.OS === 'web') {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    } else {
      // React Native环境
      const { Image } = require('react-native');
      Image.prefetch(url)
        .then(() => resolve(true))
        .catch(() => resolve(false));
    }
  });
};

/**
 * 图片URL调试工具
 * @param {string} url - 图片URL
 */
export const debugImageUrl = (url) => {
  console.log(`[${Platform.OS}] 图片URL调试:`, {
    original: url,
    transformed: transformImageUrl(url),
    platform: Platform.OS,
    devIP: Platform.OS !== 'web' ? getDevIP() : 'N/A'
  });
};
