// lib/imageUtils.js
import { Platform } from 'react-native';
import { getDevIP } from './apiConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from './apiConfig';

/**
 * 获取认证token
 * @returns {Promise<string|null>} - 认证token
 */
const getAuthToken = async () => {
  try {
    return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  } catch (error) {
    console.error(`[${Platform.OS}] 获取图片认证token失败:`, error);
    return null;
  }
};

/**
 * 转换图片URL，将localhost替换为实际IP地址
 * @param {string} url - 原始图片URL
 * @returns {string} - 转换后的URL
 */
export const transformImageUrl = (url) => {
  if (!url) return url;

  // 清理URL中的空格和特殊字符
  let cleanUrl = url.trim().replace(/\s+/g, '');

  // 如果是Web端，直接返回清理后的URL
  if (Platform.OS === 'web') {
    return cleanUrl;
  }

  // 如果URL包含localhost，替换为实际IP
  if (cleanUrl.includes('localhost:8080')) {
    const devIP = getDevIP();
    const transformedUrl = cleanUrl.replace('localhost:8080', `${devIP}:8080`);
    console.log(`[${Platform.OS}] 图片URL转换:`, {
      original: url,
      cleaned: cleanUrl,
      transformed: transformedUrl
    });
    return transformedUrl;
  }

  // 如果URL包含127.0.0.1，也替换为实际IP
  if (cleanUrl.includes('127.0.0.1:8080')) {
    const devIP = getDevIP();
    const transformedUrl = url.replace('127.0.0.1:8080', `${devIP}:8080`);
    console.log(`[${Platform.OS}] 图片URL转换:`, {
      original: url,
      transformed: transformedUrl
    });
    return transformedUrl;
  }
  
  // 其他情况直接返回原URL
  return cleanUrl;
};

/**
 * 创建带认证的图片URL（用于需要token的图片）
 * @param {string} url - 原始图片URL
 * @returns {Promise<string>} - 带认证参数的URL
 */
export const createAuthenticatedImageUrl = async (url) => {
  if (!url) return url;

  const cleanUrl = transformImageUrl(url);
  const token = await getAuthToken();

  if (!token) {
    console.warn(`[${Platform.OS}] 图片需要认证但未找到token:`, cleanUrl);
    return cleanUrl;
  }

  // 添加token作为查询参数
  const separator = cleanUrl.includes('?') ? '&' : '?';
  const authenticatedUrl = `${cleanUrl}${separator}token=${encodeURIComponent(token)}`;

  console.log(`[${Platform.OS}] 创建认证图片URL:`, {
    original: url,
    authenticated: authenticatedUrl.substring(0, 100) + '...'
  });

  return authenticatedUrl;
};

/**
 * 检查图片是否需要认证
 * @param {string} url - 图片URL
 * @returns {boolean} - 是否需要认证
 */
export const isImageAuthRequired = (url) => {
  if (!url) return false;

  // 检查是否是后端图片服务的URL
  return url.includes('/images/') ||
         url.includes('/uploads/') ||
         url.includes('/static/') ||
         url.includes('localhost:8080') ||
         url.includes('127.0.0.1:8080');
};

/**
 * 批量转换对象中的图片URL
 * @param {Object} obj - 包含图片URL的对象
 * @param {string[]} urlFields - 需要转换的URL字段名数组
 * @returns {Object} - 转换后的对象
 */
export const transformObjectImageUrls = (obj, urlFields = ['coverUrl', 'imageUrl', 'avatarUrl', 'thumbnailUrl']) => {
  if (!obj || typeof obj !== 'object') return obj;
  
  const transformed = { ...obj };
  
  urlFields.forEach(field => {
    if (transformed[field]) {
      transformed[field] = transformImageUrl(transformed[field]);
    }
  });
  
  return transformed;
};

/**
 * 批量转换数组中对象的图片URL
 * @param {Array} array - 包含对象的数组
 * @param {string[]} urlFields - 需要转换的URL字段名数组
 * @returns {Array} - 转换后的数组
 */
export const transformArrayImageUrls = (array, urlFields = ['coverUrl', 'imageUrl', 'avatarUrl', 'thumbnailUrl']) => {
  if (!Array.isArray(array)) return array;
  
  return array.map(item => transformObjectImageUrls(item, urlFields));
};

/**
 * 获取安全的图片URL，如果转换失败则返回占位符
 * @param {string} url - 原始图片URL
 * @param {string} placeholder - 占位符URL
 * @returns {string} - 安全的图片URL
 */
export const getSafeImageUrl = (url, placeholder = 'https://via.placeholder.com/300x200?text=No+Image') => {
  try {
    const transformedUrl = transformImageUrl(url);
    return transformedUrl || placeholder;
  } catch (error) {
    console.error(`[${Platform.OS}] 图片URL转换失败:`, error);
    return placeholder;
  }
};

/**
 * 预加载图片，检查图片是否可以正常加载
 * @param {string} url - 图片URL
 * @returns {Promise<boolean>} - 是否加载成功
 */
export const preloadImage = (url) => {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }
    
    if (Platform.OS === 'web') {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    } else {
      // React Native环境
      const { Image } = require('react-native');
      Image.prefetch(url)
        .then(() => resolve(true))
        .catch(() => resolve(false));
    }
  });
};

/**
 * 图片URL调试工具
 * @param {string} url - 图片URL
 */
export const debugImageUrl = (url) => {
  console.log(`[${Platform.OS}] 图片URL调试:`, {
    original: url,
    transformed: transformImageUrl(url),
    platform: Platform.OS,
    devIP: Platform.OS !== 'web' ? getDevIP() : 'N/A'
  });
};
