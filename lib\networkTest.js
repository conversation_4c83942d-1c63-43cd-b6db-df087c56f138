// lib/networkTest.js
import { Platform } from 'react-native';
import { API_BASE_URL, getDevIP } from '../constants/index';

// 网络连接测试工具
export const testNetworkConnection = async () => {
  console.log(`[${Platform.OS}] 开始网络连接测试...`);
  
  // 显示当前配置
  console.log('当前API配置:', {
    API_BASE_URL,
    platform: Platform.OS,
    devIP: Platform.OS !== 'web' ? getDevIP() : 'N/A (使用localhost)'
  });

  const tests = [
    {
      name: '基础连接测试',
      url: API_BASE_URL.replace('/api', '/health'), // 假设有健康检查端点
      timeout: 5000
    },
    {
      name: 'API端点测试',
      url: `${API_BASE_URL}/test`,
      timeout: 5000
    }
  ];

  const results = [];

  for (const test of tests) {
    try {
      console.log(`[${Platform.OS}] 测试: ${test.name}`);
      console.log(`[${Platform.OS}] URL: ${test.url}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), test.timeout);
      
      const startTime = Date.now();
      const response = await fetch(test.url, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      clearTimeout(timeoutId);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        name: test.name,
        url: test.url,
        success: true,
        status: response.status,
        statusText: response.statusText,
        duration: `${duration}ms`,
        headers: Object.fromEntries(response.headers.entries())
      };
      
      results.push(result);
      console.log(`[${Platform.OS}] ✅ ${test.name} 成功:`, result);
      
    } catch (error) {
      const result = {
        name: test.name,
        url: test.url,
        success: false,
        error: error.message,
        errorType: error.name
      };
      
      results.push(result);
      console.log(`[${Platform.OS}] ❌ ${test.name} 失败:`, result);
    }
  }

  // 输出测试总结
  console.log(`[${Platform.OS}] 网络测试完成:`, {
    total: tests.length,
    success: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    results
  });

  return results;
};

// 获取本机IP地址的建议
export const getIPAddressInstructions = () => {
  const instructions = {
    windows: [
      '1. 打开命令提示符 (cmd)',
      '2. 输入: ipconfig',
      '3. 查找 "无线局域网适配器 WLAN" 或 "以太网适配器"',
      '4. 找到 "IPv4 地址" 行，例如: *************'
    ],
    mac: [
      '1. 打开终端',
      '2. 输入: ifconfig',
      '3. 查找 en0 或 en1 接口',
      '4. 找到 "inet" 行，例如: inet *************'
    ],
    linux: [
      '1. 打开终端',
      '2. 输入: ip addr show 或 ifconfig',
      '3. 查找活动的网络接口',
      '4. 找到 inet 地址，例如: *************/24'
    ]
  };

  console.log('获取开发机器IP地址的方法:');
  Object.entries(instructions).forEach(([os, steps]) => {
    console.log(`\n${os.toUpperCase()}:`);
    steps.forEach(step => console.log(`  ${step}`));
  });

  return instructions;
};

// 网络故障排除指南
export const troubleshootNetwork = () => {
  console.log(`[${Platform.OS}] 网络故障排除指南:`);
  
  const troubleshooting = [
    '1. 确认后端服务正在运行 (端口 8080)',
    '2. 确认手机和开发机器在同一WiFi网络',
    '3. 检查防火墙设置，确保允许端口 8080',
    '4. 在 constants/index.js 中更新正确的IP地址',
    '5. 尝试在手机浏览器中访问: http://[您的IP]:8080',
    '6. 检查开发机器的网络接口是否启用',
    '7. 重启Expo开发服务器'
  ];

  troubleshooting.forEach((step, index) => {
    console.log(`  ${step}`);
  });

  return troubleshooting;
};
