// 注意：此文件已被 lib/apiServices.js 替代
// 为了向后兼容，这里重新导出新API系统的函数
import apiServices from './apiServices';

// 向后兼容的API函数 - 内部使用新的API系统
export const getIndexData = async (userId = 1) => {
  return await apiServices.content.getIndexData(userId);
};

// 向后兼容：重新导出新API系统的函数
export const getFeaturedContentList = apiServices.content.getFeaturedContentList;
export const getHotCourseList = apiServices.content.getHotCourseList;
export const getLatestCourseList = apiServices.content.getLatestCourseList;
export const getRecommendVideoList = apiServices.content.getRecommendVideoList;
