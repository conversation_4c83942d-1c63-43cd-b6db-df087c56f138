import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Text, Animated, Dimensions } from 'react-native';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

const flavors = [
  {
    name: ["Chai", "Vanilla"],
    color: "#4A90E2",
    image: require('../assets/qweqw.png'),
    nutrition: ["20g", "13g", "15", "1.8g", "1B"]
  },
  {
    name: ["Maple", "Peanut"],
    color: "#E94B4B",
    image: require('../assets/Community_image/AvatarSecond.png'),
    nutrition: ["35g", "10g", "10", "1.5g", "2B"]
  },
  {
    name: ["<PERSON>aca<PERSON>", "Coconut"],
    color: "#F4D03F",
    image: require('../assets/Community_image/AvatarThird.png'),
    nutrition: ["40g", "25g", "22", "2.2g", "1B"]
  },
  {
    name: ["Berry", "Blend"],
    color: "#8E44AD",
    image: require('../assets/Community_image/AvatarFirst.png'),
    nutrition: ["28g", "18g", "25", "2.0g", "3B"]
  }
];

function StudyCard({ topTip, title, desc }) {
  return (
    <View style={studyCardStyles.cardWrap}>
      <View style={studyCardStyles.card}>
        <Text style={studyCardStyles.topTip}>{topTip}</Text>
        <Text style={studyCardStyles.title}>{title}</Text>
        <Text style={studyCardStyles.desc}>{desc}</Text>
      </View>
      {/* 三角形缺口 */}
      <View style={studyCardStyles.triangle} />
    </View>
  );
}

export default function CommunityTeam() {
  const router = useRouter();
  const [index, setIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const bgColor = useRef(new Animated.Value(0)).current;
  const [prevColor, setPrevColor] = useState(flavors[0].color);

  const transition = () => {
    const nextIndex = (index + 1) % flavors.length;
    changeSlide(nextIndex);
  };

  useEffect(() => {
    const interval = setInterval(transition, 5000);
    return () => clearInterval(interval);
  }, [index]);

  const changeSlide = (newIndex) => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      setPrevColor(flavors[index].color);
      setIndex(newIndex);
      fadeAnim.setValue(0);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true
      }).start();
    });
  };

  const current = flavors[index];

  return (
    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ paddingTop: 60, paddingBottom: 32 }}>
        {/* 顶部栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Image source={require('../assets/FrameTwo.png')} style={[styles.backIcon]} />
          </TouchableOpacity>
          <View style={{ flex: 1 }} />
          <TouchableOpacity>
            <Image source={require('../assets/Share.png')} style={styles.shareIcon} />
          </TouchableOpacity>
          <TouchableOpacity>
            <ThemedText style={styles.headerMenu}>⋯</ThemedText>
          </TouchableOpacity>
        </View>
        {/* 头像与昵称 */}
        <View style={styles.profileRow}>
          <Image source={require('../assets/Community_image/HandsomeAvatar.png')} style={styles.avatar} />
          <View style={{ marginLeft: 12 }}>
            <ThemedText style={styles.nickname}>你好,DOG</ThemedText>
            <ThemedText style={styles.level}>Lv.30</ThemedText>
          </View>
        </View>
        {/* 今日学习任务推荐 */}

        {/* 等待配对 */}
        <View style={styles.sectionRow}>
          <ThemedText style={styles.sectionTitle} title>10位用户等待配对</ThemedText>
          <TouchableOpacity>
            <Image source={require('../assets/Arrows_right.png')} style={styles.sectionMoreIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.avatarRow}>
          {[
            require('../assets/Community_image/AvatarOne.png'),
            require('../assets/Community_image/AcatarThree.png'),
            require('../assets/Community_image/AvatarEight.png'),
            require('../assets/Community_image/AvatarFirst.png'),
            require('../assets/Community_image/AvatarFive.png'),
            require('../assets/Community_image/AvatarFour.png'),
            require('../assets/Community_image/AvatarSeven.png'),
          ].map((img, i) => (
            <Image key={i} source={img} style={styles.waitAvatar} />
          ))}
        </View>
        {/* 两个学习卡片 */}
        <View style={styles.cardRow}>
          <View style={[styles.studyCard, { backgroundColor: '#FFF9D6' }]}>
            <View style={styles.studyCardIconWrap}>
              <Text style={styles.studyCardIcon}>%</Text>
              <TouchableOpacity style={styles.studyCardOutIconWrap}>
                <Image source={require('../assets/Forward.png')} style={styles.studyCardOutIcon} />
              </TouchableOpacity>
            </View>
            <ThemedText style={styles.studyCardLabel}>Mash</ThemedText>
            <ThemedText style={styles.studyCardDesc}>进阶的数学知识</ThemedText>
          </View>
          <View style={[styles.studyCard, { backgroundColor: '#D6FFD6' }]}>
            <View style={styles.studyCardIconWrap}>
              <Text style={styles.studyCardIcon}>译</Text>
              <TouchableOpacity style={styles.studyCardOutIconWrap}>
                <Image source={require('../assets/Forward.png')} style={styles.studyCardOutIcon} />
              </TouchableOpacity>
            </View>
            <ThemedText style={styles.studyCardLabel}>English</ThemedText>
            <ThemedText style={styles.studyCardDesc}>基础的的英语知识</ThemedText>
          </View>
        </View>
        {/* PokerStack组件替换大卡片 */}

        <View style={styles.pokerStackContainer}>
          <ThemedText style={styles.sectionTitlee} title>今日学习搭子</ThemedText>
          <Animated.View style={[styles.pokerStackContent, { backgroundColor: current.color }]}>
            <Animated.View style={[styles.pokerStackInner, { opacity: fadeAnim }]}>
              <View style={styles.nameContainer}>
                <Text style={styles.word}>{current.name[0]}</Text>
                <Text style={styles.word}>{current.name[1]}</Text>
              </View>

              <Image
                source={current.image}
                style={[
                  styles.image,
                  current.name[0] === "Berry" && { width: 240, height: 240 }
                ]}
              />

              <View style={styles.nutritionPanel}>
                {current.nutrition.map((val, i) => (
                  <View style={styles.nutritionItem} key={i}>
                    <Text style={styles.nutritionValue}>{val}</Text>
                    <Text style={styles.nutritionLabel}>
                      {["辨识力", "创造力", "策略力", "持久力", "驱动力"][i]}
                    </Text>
                  </View>
                ))}
              </View>

              <View style={styles.controls}>
                {flavors.map((_, i) => (
                  <TouchableOpacity
                    key={i}
                    onPress={() => changeSlide(i)}
                    style={[
                      styles.dot,
                      i === index && styles.activeDot
                    ]}
                  />
                ))}
              </View>
            </Animated.View>
          </Animated.View>
        </View>
      </ScrollView>
    </ThemedView>

  );
}

const styles = StyleSheet.create({
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: { flexDirection: 'row', alignItems: 'center', paddingTop: 10, paddingHorizontal: 16, backgroundColor: 'transparent', marginBottom: 8 },
  backIcon: { width: 28, height: 28, marginRight: 8 },
  shareIcon: { width: 24, height: 24, marginRight: 8 },
  headerMenu: { fontSize: 24, color: '#bbb' },
  profileRow: { flexDirection: 'row', alignItems: 'center', marginLeft: 16, marginBottom: 8 },
  avatar: { width: 80, height: 80, borderRadius: 40 },
  nickname: { fontSize: 18, fontWeight: 'bold', color: '#222' },
  level: { fontSize: 14, color: '#888', marginTop: 2 },
  sectionRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginHorizontal: 16, marginTop: 18, marginBottom: 8 },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', color: '#222', paddingBottom: 4 },
  sectionTitlee: { fontSize: 16, fontWeight: 'bold', color: '#222', paddingBottom: 16 },
  sectionMore: { fontSize: 14, color: '#888' },
  taskCard: { backgroundColor: '#F2F2F2', borderRadius: 16, marginHorizontal: 16, marginBottom: 8, padding: 12, alignItems: 'center' },
  taskText: { fontSize: 15, color: '#222' },
  avatarRow: { flexDirection: 'row', alignItems: 'center', marginLeft: 16, marginBottom: 16 },
  waitAvatar: { width: 40, height: 40, borderRadius: 20, backgroundColor: '#E0E0E0', marginRight: 12 },
  cardRow: { flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 16, marginBottom: 16 },
  studyCard: { flex: 1, borderRadius: 22, marginHorizontal: 4, padding: 16, alignItems: 'flex-start', justifyContent: 'flex-start', minHeight: 160, position: 'relative', height: 50 },
  studyCardIconWrap: { flexDirection: 'row', alignItems: 'center', marginBottom: 8, width: '100%', justifyContent: 'space-between' },
  studyCardIcon: { fontSize: 28, fontWeight: 'bold', color: '#222', backgroundColor: '#fff', borderRadius: 16, paddingHorizontal: 10, paddingVertical: 2 },
  studyCardOutIconWrap: { backgroundColor: '#fff', borderRadius: 16, padding: 4 },
  studyCardOutIcon: { width: 20, height: 20 },
  studyCardLabel: { fontSize: 16, fontWeight: 'bold', color: '#222', marginBottom: 2 },
  studyCardDesc: { fontSize: 13, color: '#222', marginTop: 8, fontWeight: 'bold', transform: [{ rotate: '-10deg' }] },
  pokerStackContainer: { marginHorizontal: 16, marginBottom: 16, height: 500 },
  pokerStackContent: { flex: 1, borderRadius: 20, alignItems: 'center', justifyContent: 'center' },
  pokerStackInner: { width: 340, height: 500, borderRadius: 20, backgroundColor: '#fff0', padding: 20, alignItems: 'center', justifyContent: 'space-between' },
  nameContainer: { flexDirection: 'row', gap: 30, marginTop: 20 },
  word: { fontSize: 36, fontWeight: 'bold', color: '#fff' },
  image: { width: 240, height: 240, resizeMode: 'contain', marginVertical: 10 },
  nutritionPanel: { backgroundColor: '#fff', borderRadius: 15, padding: 10, width: '100%', flexDirection: 'row', justifyContent: 'space-between', marginBottom: 20 },
  nutritionItem: { alignItems: 'center', flex: 1 },
  nutritionValue: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  nutritionLabel: { fontSize: 10, color: '#7f8c8d', textAlign: 'center' },
  controls: { flexDirection: 'row', gap: 10 },
  dot: { width: 10, height: 10, borderRadius: 5, backgroundColor: 'rgba(255,255,255,0.4)' },
  activeDot: { backgroundColor: '#fff', transform: [{ scale: 1.3 }] },
  sectionMoreIcon: { width: 20, height: 20, resizeMode: 'contain' },
});

const studyCardStyles = StyleSheet.create({
  cardWrap: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  card: {
    width: '100%',
    backgroundColor: '#F9F199',
    borderRadius: 28,
    paddingTop: 18,
    paddingBottom: 32,
    paddingHorizontal: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
    position: 'relative',
    zIndex: 2,
  },
  topTip: {
    fontSize: 13,
    color: '#aaa',
    marginBottom: 1
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 16,
  },
  desc: {
    fontSize: 14,
    color: '#888',
    marginTop: 8,
  },
  triangle: {
    width: 0,
    height: 0,
    borderLeftWidth: 18,
    borderRightWidth: 18,
    borderTopWidth: 18,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#F9F199',
    alignSelf: 'center',
    marginTop: -8,
    zIndex: 1,
  },
}); 