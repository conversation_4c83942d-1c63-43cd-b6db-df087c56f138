// components/SafeImage.jsx
import React, { useState } from 'react';
import { Image, View, Platform } from 'react-native';
import { transformImageUrl, getSafeImageUrl } from '../lib/imageUtils';
import ThemedText from './ThemedText';

const SafeImage = ({ 
  source, 
  style, 
  placeholder = 'https://via.placeholder.com/300x200?text=No+Image',
  showDebugInfo = false,
  onError,
  onLoad,
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 处理图片URL
  const getImageSource = () => {
    if (!source) return { uri: placeholder };
    
    if (typeof source === 'string') {
      const transformedUrl = getSafeImageUrl(source, placeholder);
      return { uri: transformedUrl };
    }
    
    if (source.uri) {
      const transformedUrl = getSafeImageUrl(source.uri, placeholder);
      return { ...source, uri: transformedUrl };
    }
    
    // 如果是本地图片资源，直接返回
    return source;
  };

  const imageSource = getImageSource();

  const handleError = (error) => {
    console.error(`[${Platform.OS}] 图片加载失败:`, {
      originalSource: source,
      transformedSource: imageSource,
      error: error.nativeEvent?.error
    });
    setImageError(true);
    setIsLoading(false);
    if (onError) onError(error);
  };

  const handleLoad = (event) => {
    setIsLoading(false);
    setImageError(false);
    if (onLoad) onLoad(event);
    
    if (showDebugInfo) {
      console.log(`[${Platform.OS}] 图片加载成功:`, {
        originalSource: source,
        transformedSource: imageSource
      });
    }
  };

  const handleLoadStart = () => {
    setIsLoading(true);
    setImageError(false);
  };

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { 
        backgroundColor: '#f0f0f0', 
        justifyContent: 'center', 
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ddd',
        borderStyle: 'dashed'
      }]}>
        <ThemedText style={{ 
          fontSize: 12, 
          color: '#999', 
          textAlign: 'center',
          padding: 10
        }}>
          图片加载失败
        </ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ 
            fontSize: 10, 
            color: '#666', 
            textAlign: 'center',
            marginTop: 5
          }}>
            {typeof source === 'string' ? source : source?.uri}
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        {...props}
        source={imageSource}
        style={[style, isLoading && { opacity: 0.5 }]}
        onError={handleError}
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
      />
      {showDebugInfo && isLoading && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <ThemedText style={{ color: 'white', fontSize: 12 }}>
            加载中...
          </ThemedText>
        </View>
      )}
    </View>
  );
};

export default SafeImage;
