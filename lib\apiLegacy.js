// lib/apiLegacy.js - 兼容性层，保持向后兼容
import apiServices from './apiServices';
import { getApiBaseUrl } from './apiConfig';
import { Platform } from 'react-native';

/**
 * 兼容旧版本的API调用
 * 这个文件提供了与旧API文件相同的接口，但内部使用新的统一API系统
 */

// 兼容 lib/api.js 中的函数
export const login = apiServices.auth.login;
export const register = apiServices.auth.register;
export const forgotPassword = apiServices.auth.forgotPassword;
export const verifyUser = apiServices.auth.verifyUser;
export const validateUserBasic = apiServices.auth.validateUserBasic;
export const getCurrentUser = apiServices.auth.getCurrentUser;
export const validateToken = apiServices.auth.validateToken;
export const testNetworkConnection = apiServices.auth.testNetworkConnection;
export const getTagsByStepId = apiServices.tag.getTagsByStepId;

// 兼容 lib/contentApi.js 中的函数
export const getIndexData = apiServices.content.getIndexData;
export const getFeaturedContentList = apiServices.content.getFeaturedContentList;
export const getHotCourseList = apiServices.content.getHotCourseList;
export const getLatestCourseList = apiServices.content.getLatestCourseList;
export const getRecommendVideoList = apiServices.content.getRecommendVideoList;
export const getRecommendations = apiServices.content.getRecommendations;

// 兼容 lib/userProfile.js 中的函数
export const saveUserTagAndGenerateProfile = apiServices.profile.saveUserTagAndGenerateProfile;

// 兼容 lib/profileApi.js 中的函数
export const saveUserProfile = apiServices.profile.updateUserProfile;
export const fetchUserProfile = apiServices.profile.getUserProfile;

// 兼容旧版本的 sendSelectedIdsToBackend 函数
export const sendSelectedIdsToBackend = async (ids) => {
  console.log(`[${Platform.OS}] sendSelectedIdsToBackend (兼容模式):`, ids);
  
  try {
    // 使用新的API系统
    const response = await apiServices.profile.saveUserTagAndGenerateProfile({ tagIdList: ids });
    return response;
  } catch (error) {
    console.error(`[${Platform.OS}] sendSelectedIdsToBackend 失败:`, error);
    throw error;
  }
};

// 兼容旧版本的 API_BASE_URL 导出
export const API_BASE_URL = await getApiBaseUrl();

// 兼容旧版本的网络配置函数
export { 
  saveCustomIP, 
  getCustomIP, 
  resetNetworkConfig,
  debugNetworkConfig as getNetworkConfigInfo
} from './apiConfig';

/**
 * 迁移提示函数
 * 在控制台输出迁移建议
 */
export const showMigrationTip = (oldFunction, newFunction) => {
  console.warn(`[${Platform.OS}] 迁移提示: 建议使用新的API系统`);
  console.warn(`旧方式: ${oldFunction}`);
  console.warn(`新方式: ${newFunction}`);
  console.warn('详情请查看 lib/apiServices.js');
};

// 导出新的API系统（推荐使用）
export { default as apiServices } from './apiServices';
export { default as apiClient } from './apiClient';
export * from './apiConfig';
