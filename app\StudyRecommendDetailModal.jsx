import React, { useEffect, useRef, useState } from 'react';
import { View, Animated, StyleSheet, Dimensions, Pressable, Modal, TouchableOpacity, Image, useWindowDimensions, Platform } from 'react-native';
import ThemedCard from '../components/ThemedCard';
import ThemedText from '../components/ThemedText';
import ArrowImg from '../assets/Plan_image/Arrows.png';
import { LinearGradient } from 'expo-linear-gradient';
import AiImg from '../assets/dog.png';
import { shadowPresets } from '../utils/shadowUtils';

const { width, height } = Dimensions.get('window');

// 定义每张卡片的渐变色（与StudyRecommendCarousel保持一致）
const cardGradients = [
  ['#FFE2BD', '#E5F5FF'], // 第一张卡片：FFE2BD到E5F5FF
  ['#F1E2FF', '#FFE2BD'], // 第二张卡片：F1E2FF到FFE2BD
  ['#E8F7DE', '#FFE2BD'], // 第三张卡片：E8F7DE到FFE2BD
];

// 获取卡片渐变色的函数
const getCardGradient = (card) => {
  // 如果有carouselIndex，优先使用
  if (card?.carouselIndex !== undefined && card?.carouselIndex !== null) {
    const realIndex = card.carouselIndex % 3;
    return cardGradients[realIndex] || ['#e3e3e3', '#ababab'];
  }

  // 如果没有carouselIndex，根据卡片标题确定
  if (card?.item?.title) {
    const title = card.item.title;
    if (title === '考试规划') {
      return cardGradients[0]; // ['#FFE2BD', '#E5F5FF']
    } else if (title === '学习规划') {
      return cardGradients[1]; // ['#F1E2FF', '#FFE2BD']
    } else if (title === '兴趣推荐') {
      return cardGradients[2]; // ['#E8F7DE', '#FFE2BD']
    }
  }

  return ['#e3e3e3', '#ababab']; // 默认渐变色
};

export default function StudyRecommendDetailModal({ visible, card, onClose, onAfterClose }) {
  const anim = useRef(new Animated.Value(0)).current;
  const [isClosing, setIsClosing] = useState(false);
  const { width, height } = useWindowDimensions();



  useEffect(() => {
    if (visible && card) {
      setIsClosing(false);
      anim.setValue(0);
      Animated.timing(anim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true, // 确保在原生线程执行
      }).start();
    }
  }, [visible, card]);

  const handleClose = () => {
    setIsClosing(true);
    Animated.timing(anim, {
      toValue: 0,
      duration: 600,
      useNativeDriver: true, // 确保在原生线程执行
    }).start(() => {
      setIsClosing(false);
      onClose();
      if (onAfterClose) onAfterClose(); // 动画完全结束后通知父组件
    });
  };

  if ((!visible && !isClosing) || !card) return null;

  // 动画起点
  const { x, y, width: cardW, height: cardH, scale: cardScale = 1 } = card.layout;
  const finalTop = 80; // 顶部边距由24调整为12
  const pageCenterX = width / 2;

  // 计算动画插值
  // 目标中心点
  const targetLeft = pageCenterX - cardH / 2;
  const targetTop = finalTop;
  // 起点
  const startLeft = x;
  const startTop = y;

  // 内容区动画终点
  const contentStartTop = y + cardH + 24;
  const contentEndTop = finalTop + cardH - 70;

  // 平台兼容动画样式
  let animatedCardStyle, animatedContentStyle;
  if (Platform.OS === 'web') {
    // web端用left/top
    animatedCardStyle = {
      position: 'absolute',
      left: anim.interpolate({ inputRange: [0, 1], outputRange: [startLeft, targetLeft] }),
      top: anim.interpolate({ inputRange: [0, 1], outputRange: [startTop, targetTop] }),
      width: cardW,
      height: cardH,
      zIndex: 2,
      transform: [
        { translateX: anim.interpolate({ inputRange: [0, 1], outputRange: [0, (cardH - cardW) / 2] }) },
        { translateY: anim.interpolate({ inputRange: [0, 1], outputRange: [0, (cardW - cardH) / 2] }) },
        { rotateZ: anim.interpolate({ inputRange: [0, 1], outputRange: ['0deg', '90deg'] }) },
        { scale: anim.interpolate({ inputRange: [0, 1], outputRange: [cardScale, 1.08] }) },
      ],
    };
    animatedContentStyle = {
      position: 'absolute',
      left: 0,
      right: 0,
      top: anim.interpolate({ inputRange: [0, 1], outputRange: [contentStartTop, contentEndTop] }),
      alignItems: 'center',
      width: '100%',
      opacity: anim,
    };
  } else {
    // app端用transform
    const translateX = anim.interpolate({ inputRange: [0, 1], outputRange: [startLeft - targetLeft, 0] });
    const translateY = anim.interpolate({ inputRange: [0, 1], outputRange: [startTop - targetTop, 0] });
    animatedCardStyle = {
      position: 'absolute',
      left: targetLeft,
      top: targetTop,
      width: cardW,
      height: cardH,
      zIndex: 2,
      transform: [
        { translateX },
        { translateY },
        { translateX: anim.interpolate({ inputRange: [0, 1], outputRange: [0, (cardH - cardW) / 2] }) },
        { translateY: anim.interpolate({ inputRange: [0, 1], outputRange: [0, (cardW - cardH) / 2] }) },
        { rotateZ: anim.interpolate({ inputRange: [0, 1], outputRange: ['0deg', '90deg'] }) },
        { scale: anim.interpolate({ inputRange: [0, 1], outputRange: [cardScale, 1.08] }) },
      ],
    };
    // 内容区同理
    const contentTranslateY = anim.interpolate({ inputRange: [0, 1], outputRange: [contentStartTop - contentEndTop, 0] });
    animatedContentStyle = {
      position: 'absolute',
      left: 0,
      right: 0,
      top: contentEndTop,
      alignItems: 'center',
      width: '100%',
      opacity: anim,
      transform: [
        { translateY: contentTranslateY },
      ],
    };
  }

  return (
    <Modal 
      visible={visible || isClosing} 
      transparent 
      animationType="none" 
      onRequestClose={handleClose}
      hardwareAccelerated={Platform.OS === 'android'} // Android硬件加速
    >
      <View style={[styles.fullScreen, {backgroundColor: '#f5f6f7'}]} pointerEvents="box-none">
        {/* 动画卡片整体旋转，卡片大小固定 */}
        <Animated.View style={animatedCardStyle}>
          {/* 退出按钮 */}
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 12,
              left: 12,
              zIndex: 10,
              width: 32,
              height: 32,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={handleClose}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }} // 增加点击区域
          >
            <Image source={ArrowImg} style={{ width: 24, height: 24 }} resizeMode="contain" />
          </TouchableOpacity>
          <LinearGradient
            colors={getCardGradient(card)}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={[styles.card, styles.landscapeCard]}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                transform: [
                  {
                    rotateZ: anim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '-90deg'],
                    }),
                  },
                ],
              }}
            >
              <ThemedText style={[styles.title, {textAlign: 'center', alignSelf: 'center'}]}>{card.item.title}</ThemedText>
              <ThemedText style={[styles.desc, {textAlign: 'center', alignSelf: 'center'}]}>{card.item.desc}</ThemedText>
            </Animated.View>
          </LinearGradient>
        </Animated.View>
        {/* 新增弹窗内容区 */}
        <Animated.View style={animatedContentStyle}>
          {/* AI形象+气泡 */}
          <View style={{width: 340, flexDirection: 'row',alignItems: 'center', marginBottom: 5}}>
            <Image source={AiImg} style={{width: 82, height: 82, marginRight: 10}} />
            <View style={{flex: 1, flexDirection: 'row', justifyContent: 'flex-end'}}>
              <View style={{backgroundColor: '#ededed', borderRadius: 10, paddingHorizontal: 15, paddingVertical: 10, maxWidth: 270,}}>
                <ThemedText style={{fontSize: 16, color: '#444'}}>快来设置你的考试时间和科目吧~</ThemedText>
              </View>
            </View>
          </View>
          {/* 表单区 */}
          <View style={{backgroundColor: '#ededed', borderRadius: 18, padding: 13, width: 300, marginBottom: 18,marginLeft: 60}}>
            <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 12}}>
              <ThemedText style={{fontSize: 16, color: '#222', width: 80}}>考试时间</ThemedText>
              <View style={{flex: 1, backgroundColor: '#e3e3e3', flexDirection: 'row', alignItems: 'center',maxWidth: 150}}>
                <ThemedText style={{fontSize: 15, color: '#888'}}>2025 年 07 月 17 日</ThemedText>
              </View>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <ThemedText style={{fontSize: 16, color: '#222', width: 80}}>考试科目</ThemedText>
              <View style={{flex: 1, backgroundColor: '#e3e3e3',maxWidth: 150}}>
                <ThemedText style={{fontSize: 15, color: '#888'}}>英语</ThemedText>
              </View>
            </View>
          </View>
          {/* 进度生成提示 */}
          <View style={{alignItems: 'center', marginBottom: 18}}>
            <ThemedText style={{fontSize: 15, color: '#888'}}>小艾正在定制专属于你的考试规划……</ThemedText>
            <View style={{width: 60, height: 4, backgroundColor: '#ddd', borderRadius: 2, marginTop: 8}} />
          </View>
          {/* 三大功能卡片区 */}
          <View style={{width: 340, backgroundColor: '#ededed', borderRadius: 24, padding: 18}}>
            <TouchableOpacity 
              style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 18}}
              activeOpacity={0.7}
              hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
            >
              <ThemedText style={{fontSize: 17, color: '#222'}}>查看生成的考试复习进度规划</ThemedText>
              <Image source={ArrowImg} style={{width: 22, height: 22}} />
            </TouchableOpacity>
            <View style={{height: 1, backgroundColor: '#ccc', marginBottom: 12}} />
            <TouchableOpacity 
              style={{marginBottom: 18}}
              activeOpacity={0.7}
              hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
            >
              <ThemedText style={{fontSize: 17, color: '#222'}}>查看分析考试重点难点</ThemedText>
              <ThemedText style={{fontSize: 13, color: '#888', marginTop: 2}}>制定专属于你的答题策略</ThemedText>
              <Image source={ArrowImg} style={{width: 22, height: 22, position: 'absolute', right: 0, top: 0}} />
            </TouchableOpacity>
            <View style={{height: 1, backgroundColor: '#ccc', marginBottom: 12}} />
            <TouchableOpacity
              activeOpacity={0.7}
              hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
            >
              <ThemedText style={{fontSize: 17, color: '#222'}}>查看思维导图知识框架</ThemedText>
              <ThemedText style={{fontSize: 13, color: '#888', marginTop: 2}}>辅助记忆和理解，使学习路径更清晰直观</ThemedText>
              <Image source={ArrowImg} style={{width: 22, height: 22, position: 'absolute', right: 0, top: 0}} />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  mask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.18)',
    zIndex: 1,
  },
  fullScreen: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  animatedCard: {
    zIndex: 2,
  },
  card: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 28,
    backgroundColor: 'transparent',
    ...shadowPresets.medium,
    padding: 18,
  },
  landscapeCard: {
    width: '100%',
    height: '100%',
    flexDirection: 'row',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 12,
  },
  desc: {
    fontSize: 15,
    color: '#444',
    textAlign: 'center',
  },
  detailContent: {
    width: '100%',
    minHeight: 120,
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    alignItems: 'center',
    padding: 28,
    zIndex: 1,
  },
}); 