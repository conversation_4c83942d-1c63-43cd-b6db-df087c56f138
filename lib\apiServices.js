// lib/apiServices.js - 统一的API服务层
import apiClient from './apiClient';
import { Platform } from 'react-native';

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  async login(userAccount, userPassword) {
    console.log(`[${Platform.OS}] 开始登录:`, { userAccount });
    
    const response = await apiClient.authRequest('/login', {
      method: 'POST',
      body: { userAccount, userPassword }
    });

    if (!response.ok) {
      throw new Error(response.data || '登录失败');
    }

    // 保存token
    if (response.data && response.data.token) {
      await apiClient.saveAuthToken(response.data.token);
    }

    console.log(`[${Platform.OS}] 登录成功`);
    return response.data;
  },

  /**
   * 用户注册
   */
  async register(userAccount, userPassword, checkPassword) {
    console.log(`[${Platform.OS}] 开始注册:`, { userAccount });
    
    const response = await apiClient.authRequest('/register', {
      method: 'POST',
      body: { userAccount, userPassword, checkPassword }
    });

    if (!response.ok) {
      throw new Error(response.data || '注册失败');
    }

    console.log(`[${Platform.OS}] 注册成功`);
    return response.data;
  },

  /**
   * 忘记密码
   */
  async forgotPassword(userAccount) {
    console.log(`[${Platform.OS}] 忘记密码:`, { userAccount });
    
    const response = await apiClient.authRequest('/forgot-password', {
      method: 'POST',
      body: { userAccount }
    });

    if (!response.ok) {
      throw new Error(response.data || '操作失败');
    }

    return response.data;
  },

  /**
   * 轻量级用户验证
   */
  async verifyUser() {
    console.log(`[${Platform.OS}] 开始轻量级用户验证`);
    
    const response = await apiClient.get('/user/verify');

    if (!response.ok) {
      throw new Error('用户验证失败');
    }

    console.log(`[${Platform.OS}] 用户验证成功`);
    return response.data;
  },

  /**
   * 登出
   */
  async logout() {
    console.log(`[${Platform.OS}] 用户登出`);
    await apiClient.removeAuthToken();
    return true;
  }
};

/**
 * 内容相关API
 */
export const contentApi = {
  /**
   * 获取首页数据
   */
  async getIndexData(userId = 1) {
    console.log(`[${Platform.OS}] 获取首页数据:`, { userId });
    
    const response = await apiClient.get(`/index?userId=${userId}`, {
      transformImages: true // 自动转换图片URL
    });

    if (!response.ok) {
      throw new Error('获取首页数据失败');
    }

    return response.data;
  },

  /**
   * 获取精品内容列表
   */
  async getFeaturedContentList() {
    const data = await this.getIndexData();
    const featuredData = data?.data?.featuredContentList || [];
    return { data: featuredData };
  },

  /**
   * 获取热门课程列表
   */
  async getHotCourseList() {
    const data = await this.getIndexData();
    const hotData = data?.data?.hotCourseList || [];
    return { data: hotData };
  },

  /**
   * 获取最新课程列表
   */
  async getLatestCourseList() {
    const data = await this.getIndexData();
    const latestData = data?.data?.latestCourseList || [];
    return { data: latestData };
  },

  /**
   * 获取推荐视频列表
   */
  async getRecommendVideoList(userId) {
    const data = await this.getIndexData(userId);
    const recommendData = data?.data?.recommendVideoList || [];
    return { data: recommendData };
  }
};

/**
 * 用户资料相关API
 */
export const profileApi = {
  /**
   * 保存用户标签并生成AI画像
   */
  async saveUserTagAndGenerateProfile(selections) {
    console.log(`[${Platform.OS}] 保存用户标签并生成AI画像:`, selections);
    
    // 将selections对象转换为tagIdList数组
    const tagIdList = Object.values(selections).flat();
    
    const response = await apiClient.post('/profile/save', {
      tagIdList
    });

    if (!response.ok || response.data?.code !== 0) {
      throw new Error(response.data?.message || '保存失败');
    }

    console.log(`[${Platform.OS}] AI画像生成成功`);
    return response.data.data;
  },

  /**
   * 获取用户资料
   */
  async getUserProfile() {
    console.log(`[${Platform.OS}] 获取用户资料`);
    
    const response = await apiClient.get('/user/profile');

    if (!response.ok) {
      throw new Error('获取用户资料失败');
    }

    return response.data;
  },

  /**
   * 更新用户资料
   */
  async updateUserProfile(profileData) {
    console.log(`[${Platform.OS}] 更新用户资料:`, profileData);
    
    const response = await apiClient.put('/user/profile', profileData);

    if (!response.ok) {
      throw new Error('更新用户资料失败');
    }

    return response.data;
  }
};

/**
 * 标签相关API
 */
export const tagApi = {
  /**
   * 根据步骤ID获取标签列表
   */
  async getTagsByStepId(stepId) {
    console.log(`[${Platform.OS}] 获取标签列表:`, { stepId });
    
    const response = await apiClient.get(`/tag/step/${stepId}`);

    if (!response.ok) {
      throw new Error('获取标签失败');
    }

    // 处理不同的响应格式
    let tags = [];
    const data = response.data;
    
    if (Array.isArray(data)) {
      tags = data;
    } else if (data?.data && Array.isArray(data.data)) {
      tags = data.data;
    } else if (data?.success && data?.data && Array.isArray(data.data)) {
      tags = data.data;
    }

    // 转换为前端需要的格式
    return tags.map(tag => ({
      id: tag.tag_id || tag.tagId || tag.id,
      text: tag.tag_text || tag.tagText || tag.text,
      color: tag.tag_color || tag.tagColor || tag.color || '#FF6B35'
    }));
  }
};

/**
 * 通用API工具
 */
export const commonApi = {
  /**
   * 健康检查
   */
  async healthCheck() {
    const response = await apiClient.get('/health', {
      requireAuth: false
    });
    return response.data;
  },

  /**
   * 获取服务器时间
   */
  async getServerTime() {
    const response = await apiClient.get('/time', {
      requireAuth: false
    });
    return response.data;
  }
};

// 导出所有API服务
export default {
  auth: authApi,
  content: contentApi,
  profile: profileApi,
  tag: tagApi,
  common: commonApi
};
