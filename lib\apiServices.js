// lib/apiServices.js - 统一的API服务层
import apiClient from './apiClient';
import { Platform } from 'react-native';

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  async login(userAccount, userPassword) {
    console.log(`[${Platform.OS}] 开始登录:`, { userAccount });
    
    const response = await apiClient.authRequest('/login', {
      method: 'POST',
      body: { userAccount, userPassword }
    });

    if (!response.ok) {
      throw new Error(response.data || '登录失败');
    }

    // 保存token
    if (response.data && response.data.token) {
      await apiClient.saveAuthToken(response.data.token);
    }

    console.log(`[${Platform.OS}] 登录成功`);
    return response.data;
  },

  /**
   * 用户注册
   */
  async register(userAccount, userPassword, checkPassword) {
    console.log(`[${Platform.OS}] 开始注册:`, { userAccount });
    
    const response = await apiClient.authRequest('/register', {
      method: 'POST',
      body: { userAccount, userPassword, checkPassword }
    });

    if (!response.ok) {
      throw new Error(response.data || '注册失败');
    }

    console.log(`[${Platform.OS}] 注册成功`);
    return response.data;
  },

  /**
   * 忘记密码
   */
  async forgotPassword(userAccount) {
    console.log(`[${Platform.OS}] 忘记密码:`, { userAccount });
    
    const response = await apiClient.authRequest('/forgot-password', {
      method: 'POST',
      body: { userAccount }
    });

    if (!response.ok) {
      throw new Error(response.data || '操作失败');
    }

    return response.data;
  },

  /**
   * 轻量级用户验证
   */
  async verifyUser() {
    console.log(`[${Platform.OS}] 开始轻量级用户验证`);
    
    const response = await apiClient.get('/user/verify');

    if (!response.ok) {
      throw new Error('用户验证失败');
    }

    console.log(`[${Platform.OS}] 用户验证成功`);
    return response.data;
  },

  /**
   * 登出
   */
  async logout() {
    console.log(`[${Platform.OS}] 用户登出`);
    await apiClient.removeAuthToken();
    return true;
  },

  /**
   * 获取当前用户信息
   */
  async getCurrentUser() {
    console.log(`[${Platform.OS}] 获取当前用户信息`);

    const response = await apiClient.authRequest('/me', {
      method: 'GET',
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error('获取用户信息失败');
    }

    // 处理不同的响应格式
    let userData = null;
    const data = response.data;

    if (data.data && data.data.id && data.data.userAccount) {
      userData = data.data;
    } else if (data.id && data.userAccount) {
      userData = data;
    } else if (data.user && data.user.id && data.user.userAccount) {
      userData = data.user;
    }

    if (userData) {
      console.log(`[${Platform.OS}] 用户信息获取成功:`, userData);
      return userData;
    } else {
      throw new Error('用户数据格式无效');
    }
  },

  /**
   * 轻量级用户验证（只获取 id 和 userAccount）
   */
  async validateUserBasic() {
    console.log(`[${Platform.OS}] 开始轻量级用户验证`);

    try {
      const response = await apiClient.authRequest('/me', {
        method: 'GET',
        requireAuth: true
      });

      if (!response.ok) {
        return null;
      }

      const data = response.data;
      let basicUserData = null;

      // 只提取 id 和 userAccount，忽略其他字段以减少处理
      if (data.data && (data.data.id || data.data.userAccount)) {
        basicUserData = {
          id: data.data.id,
          userAccount: data.data.userAccount
        };
      } else if (data.id || data.userAccount) {
        basicUserData = {
          id: data.id,
          userAccount: data.userAccount
        };
      } else if (data.user && (data.user.id || data.user.userAccount)) {
        basicUserData = {
          id: data.user.id,
          userAccount: data.user.userAccount
        };
      }

      if (basicUserData && (basicUserData.id || basicUserData.userAccount)) {
        console.log(`[${Platform.OS}] 轻量级用户验证成功:`, basicUserData);
        return basicUserData;
      } else {
        console.log(`[${Platform.OS}] 轻量级用户验证数据格式无效`);
        return null;
      }
    } catch (error) {
      console.error(`[${Platform.OS}] 轻量级用户验证错误:`, error);
      return null;
    }
  },

  /**
   * 验证JWT Token是否有效
   */
  async validateToken() {
    // 注释掉token验证逻辑，直接返回true保持登录状态
    return true;
  },

  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    console.log(`[${Platform.OS}] 测试网络连接`);

    try {
      const response = await apiClient.authRequest('/test', {
        method: 'GET',
        requireAuth: false,
        timeout: 5000
      });

      console.log(`[${Platform.OS}] 网络连接测试成功`);
      return true;
    } catch (error) {
      console.error(`[${Platform.OS}] 网络连接测试失败:`, error);
      return false;
    }
  }
};

/**
 * 内容相关API
 */
export const contentApi = {
  /**
   * 获取首页数据（支持游客模式）
   */
  async getIndexData(userId = null) {
    console.log(`[${Platform.OS}] 获取首页数据:`, { userId, mode: userId ? '登录用户' : '游客模式' });

    // 构建请求URL，游客模式不传userId参数
    const endpoint = userId ? `/index?userId=${userId}` : '/index';

    const response = await apiClient.get(endpoint, {
      transformImages: true, // 自动转换图片URL
      requireAuth: !!userId // 游客模式不需要认证
    });

    if (!response.ok) {
      throw new Error('获取首页数据失败');
    }

    console.log(`[${Platform.OS}] 首页API原始返回:`, response.data);

    // 处理标准的API返回结构：{ code: 0, message: "ok", data: {...} }
    if (response.data.code === 0 && response.data.data) {
      console.log(`[${Platform.OS}] 解析首页数据:`, response.data.data);
      return { data: response.data.data }; // 返回标准格式
    } else if (response.data.data) {
      // 兼容直接返回 data 的情况
      return { data: response.data.data };
    } else {
      // 兼容直接返回内容的情况
      return { data: response.data };
    }
  },

  /**
   * 获取精品内容列表
   */
  async getFeaturedContentList() {
    const response = await this.getIndexData();
    const data = response.data || {};

    // 优先使用新的数据字段，保持对旧数据结构的兼容性
    const featuredData = data.featuredContentList || [];

    console.log(`[${Platform.OS}] 精品内容数据:`, featuredData.length);
    return { data: featuredData };
  },

  /**
   * 获取热门课程列表
   */
  async getHotCourseList() {
    const response = await this.getIndexData();
    const data = response.data || {};

    // 优先使用新的数据字段，保持对旧数据结构的兼容性
    const hotData = data.hotCourseList || [];

    console.log(`[${Platform.OS}] 热门课程数据:`, hotData.length);
    return { data: hotData };
  },

  /**
   * 获取最新课程列表
   */
  async getLatestCourseList() {
    const response = await this.getIndexData();
    const data = response.data || {};

    // 优先使用新的数据字段，保持对旧数据结构的兼容性
    const latestData = data.latestCourseList || [];

    console.log(`[${Platform.OS}] 最新课程数据:`, latestData.length);
    return { data: latestData };
  },

  /**
   * 获取推荐视频列表（支持游客模式）
   */
  async getRecommendVideoList(userId = null) {
    const response = await this.getIndexData(userId);
    const data = response.data || {};

    // 优先使用兴趣推荐，如果没有则使用画像推荐，最后兼容旧结构
    const interestRecommend = data.interestRecommend || [];
    const profileRecommend = data.profileRecommend || [];
    const oldRecommendData = data.recommendVideoList || [];

    // 游客模式优先使用通用推荐数据
    const guestRecommend = data.guestRecommend || data.defaultRecommend || [];

    let recommendData;
    if (!userId) {
      // 游客模式：优先使用游客推荐，然后是通用数据
      recommendData = guestRecommend.length > 0 ? guestRecommend :
        oldRecommendData.length > 0 ? oldRecommendData :
          interestRecommend.length > 0 ? interestRecommend :
            profileRecommend;
    } else {
      // 登录用户：优先使用个性化推荐
      recommendData = interestRecommend.length > 0 ? interestRecommend :
        profileRecommend.length > 0 ? profileRecommend :
          guestRecommend.length > 0 ? guestRecommend :
            oldRecommendData;
    }

    console.log(`[${Platform.OS}] 推荐数据解析:`, {
      mode: userId ? '登录用户' : '游客模式',
      interestRecommend: interestRecommend.length,
      profileRecommend: profileRecommend.length,
      guestRecommend: guestRecommend.length,
      oldRecommendData: oldRecommendData.length,
      finalData: recommendData.length
    });

    return { data: recommendData };
  },

  /**
   * 获取推荐内容（支持游客模式，自动检测是否有token）
   */
  async getRecommendations(userId = null) {
    console.log(`[${Platform.OS}] 开始获取推荐内容:`, { userId, mode: userId ? '登录用户' : '游客模式' });

    // 检查是否有token来决定是否需要认证
    const token = await apiClient.getAuthToken();
    const hasAuth = !!token;

    // 构建请求URL
    const endpoint = userId ? `/index?userId=${userId}` : '/index';

    const response = await apiClient.get(endpoint, {
      transformImages: true, // 自动转换图片URL
      requireAuth: hasAuth && !!userId // 有token且有userId时才需要认证
    });

    if (!response.ok) {
      throw new Error(`获取推荐失败：${response.data}`);
    }

    console.log(`[${Platform.OS}] API原始返回数据:`, response.data);

    // 处理标准的API返回结构：{ code: 0, message: "ok", data: {...} }
    if (response.data.code === 0 && response.data.data) {
      console.log(`[${Platform.OS}] 解析推荐数据:`, response.data.data);
      return response.data.data; // 返回 data 部分，包含 interestRecommend, profileRecommend 等
    } else if (response.data.data) {
      // 兼容直接返回 data 的情况
      return response.data.data;
    } else {
      // 兼容直接返回推荐内容的情况
      return response.data;
    }
  }
};

/**
 * 用户资料相关API
 */
export const profileApi = {
  /**
   * 保存用户标签并生成AI画像
   */
  async saveUserTagAndGenerateProfile(selections) {
    console.log(`[${Platform.OS}] 保存用户标签并生成AI画像:`, selections);

    // 检查是否有认证token
    const token = await apiClient.getAuthToken();
    if (!token) {
      console.error(`[${Platform.OS}] 保存标签失败: 用户未登录`);
      throw new Error('请先登录后再保存标签');
    }

    console.log(`[${Platform.OS}] 找到认证token，继续保存标签`);

    // 将selections对象转换为tagIdList数组
    const tagIdList = Object.values(selections).flat();

    const response = await apiClient.post('/profile/save', {
      tagIdList
    }, {
      requireAuth: true // 明确要求认证
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Token可能已过期，清除token并提示重新登录
        await apiClient.removeAuthToken();
        throw new Error('登录已过期，请重新登录');
      }
      throw new Error(response.data?.message || '保存失败');
    }

    if (response.data?.code !== 0) {
      throw new Error(response.data?.message || '保存失败');
    }

    console.log(`[${Platform.OS}] AI画像生成成功`);
    return response.data.data;
  },

  /**
   * 获取用户资料
   */
  async getUserProfile() {
    console.log(`[${Platform.OS}] 获取用户资料`);
    
    const response = await apiClient.get('/user/profile');

    if (!response.ok) {
      throw new Error('获取用户资料失败');
    }

    return response.data;
  },

  /**
   * 更新用户资料
   */
  async updateUserProfile(profileData) {
    console.log(`[${Platform.OS}] 更新用户资料:`, profileData);
    
    const response = await apiClient.put('/user/profile', profileData);

    if (!response.ok) {
      throw new Error('更新用户资料失败');
    }

    return response.data;
  }
};

/**
 * 标签相关API
 */
export const tagApi = {
  /**
   * 根据步骤ID获取标签列表
   */
  async getTagsByStepId(stepId) {
    console.log(`[${Platform.OS}] 获取标签列表:`, { stepId });
    
    const response = await apiClient.get(`/tag/step/${stepId}`);

    if (!response.ok) {
      throw new Error('获取标签失败');
    }

    // 处理不同的响应格式
    let tags = [];
    const data = response.data;
    
    if (Array.isArray(data)) {
      tags = data;
    } else if (data?.data && Array.isArray(data.data)) {
      tags = data.data;
    } else if (data?.success && data?.data && Array.isArray(data.data)) {
      tags = data.data;
    }

    // 转换为前端需要的格式
    return tags.map(tag => ({
      id: tag.tag_id || tag.tagId || tag.id,
      text: tag.tag_text || tag.tagText || tag.text,
      color: tag.tag_color || tag.tagColor || tag.color || '#FF6B35'
    }));
  }
};

/**
 * 通用API工具
 */
export const commonApi = {
  /**
   * 健康检查
   */
  async healthCheck() {
    const response = await apiClient.get('/health', {
      requireAuth: false
    });
    return response.data;
  },

  /**
   * 获取服务器时间
   */
  async getServerTime() {
    const response = await apiClient.get('/time', {
      requireAuth: false
    });
    return response.data;
  }
};

// 导出所有API服务
export default {
  auth: authApi,
  content: contentApi,
  profile: profileApi,
  tag: tagApi,
  common: commonApi
};
