import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Text,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../../components/ThemedText';
import StatusBox from '../../../components/StatusBox';
import { shadowPresets } from '../../../utils/shadowUtils';

const ExamFour = () => {
  const router = useRouter();
  const [selectedOption, setSelectedOption] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [options, setOptions] = useState([]);

  // 题目数据
  const questionData = {
    question: "What is the primary function of the cell membrane?",
    correctAnswer: 'A',
    options: [
      { id: 'A', text: 'To control what enters and exits the cell' },
      { id: 'B', text: 'To produce energy for the cell' },
      { id: 'C', text: 'To store genetic information' },
      { id: 'D', text: 'To synthesize proteins' }
    ]
  };

  // 随机打乱选项顺序
  const shuffleOptions = (optionsArray) => {
    const shuffled = [...optionsArray];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // 初始化时打乱选项
  useEffect(() => {
    setOptions(shuffleOptions(questionData.options));
  }, []);

  // 处理选项点击
  const handleOptionPress = (optionId) => {
    if (selectedOption || showResult) return; // 防止重复点击

    setSelectedOption(optionId);
    const correct = optionId === questionData.correctAnswer;
    setIsCorrect(correct);
    setShowResult(true);

    if (correct) {
      // 答对：2秒后跳转到下一题
      setTimeout(() => {
        router.push('/Train/ExamSprint/ExamFive');
      }, 2000);
    } else {
      // 答错：2秒后隐藏弹窗
      setTimeout(() => {
        setShowResult(false);
        setSelectedOption(null);
      }, 2000);
    }
  };

  // 处理StatusBox按钮点击
  const handleStatusBoxPress = () => {
    if (isCorrect) {
      router.push('/Train/ExamSprint/ExamFive');
    } else {
      setShowResult(false);
      setSelectedOption(null);
    }
  };

  // 获取选项样式
  const getOptionStyle = (optionId) => {
    if (!selectedOption) return styles.option;

    if (optionId === selectedOption) {
      return isCorrect ? styles.optionCorrect : styles.optionWrong;
    }
    return styles.option;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#F5F5F5" />

      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Image
            source={require('../../../assets/FrameTwo.png')}
            style={styles.backIcon}
          />
        </TouchableOpacity>

        {/* 进度条 */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '80%' }]} />
          </View>
        </View>

        <TouchableOpacity>
          <Image
            source={require('../../../assets/FrameTwo.png')}
            style={styles.settingsIcon}
          />
        </TouchableOpacity>
      </View>

      {/* 题目内容 */}
      <View style={styles.content}>
        <ThemedText style={styles.question}>
          {questionData.question}
        </ThemedText>

        {/* 选项列表 */}
        <View style={styles.optionsContainer}>
          {options.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={getOptionStyle(option.id)}
              onPress={() => handleOptionPress(option.id)}
              disabled={selectedOption !== null}
            >
              <Text style={styles.optionLabel}>{option.id}.</Text>
              <Text style={styles.optionText}>{option.text}</Text>
              {selectedOption === option.id && isCorrect && (
                <Image
                  source={require('../../../assets/FrameTwo.png')}
                  style={styles.checkIcon}
                  tintColor="#4CAF50"
                />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* 结果弹窗 */}
      {showResult && (
        <View style={styles.statusBoxContainer}>
          <StatusBox
            type={isCorrect ? "success" : "error"}
            title={isCorrect ? "答对了!" : "答错了!"}
            message={isCorrect ? "干的好,快来回答下一道题!" : "不要担心,,马上就要成功了!"}
            buttonText={isCorrect ? "继续" : "重新挑战"}
            onPress={handleStatusBoxPress}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  safeArea: {
    height: 50,
    backgroundColor: '#F5F5F5',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
    backgroundColor: '#FFF8F3',
  },
  backIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  progressContainer: {
    flex: 1,
    marginHorizontal: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#FFEEDB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFC47B',
    borderRadius: 4,
  },
  settingsIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  question: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    lineHeight: 32,
    marginBottom: 60,
  },
  optionsContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: 100,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    ...shadowPresets.medium,
  },
  optionCorrect: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#4CAF50',
    ...shadowPresets.medium,
  },
  optionWrong: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#F44336',
    ...shadowPresets.medium,
  },
  optionLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 12,
    minWidth: 24,
  },
  optionText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  checkIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  statusBoxContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
});

export default ExamFour;