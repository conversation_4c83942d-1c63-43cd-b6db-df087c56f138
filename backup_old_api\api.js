import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 网络配置 - 支持动态获取和手动配置
const NETWORK_CONFIG = {
  development: {
    web: 'http://localhost:8080/api/auth',
    android: 'http://********:8080/api/auth', // Android模拟器
    ios: 'http://localhost:8080/api/auth', // iOS模拟器
    // 真机测试时，使用电脑的实际局域网IP
    androidDevice: 'http://**************:8080/api/auth',
    iosDevice: 'http://**************:8080/api/auth',
  }
};

const STORAGE_KEYS = {
  CUSTOM_IP: 'custom_ip_address',
  AUTH_TOKEN: 'auth_token', // 新增JWT存储键
};

/**
 * 保存自定义IP地址
 */
export const saveCustomIP = async (ip) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.CUSTOM_IP, ip);
    console.log('保存自定义IP:', ip);
    return true;
  } catch (error) {
    console.error('保存IP失败:', error);
    return false;
  }
};

/**
 * 获取自定义IP地址
 */
export const getCustomIP = async () => {
  try {
    const ip = await AsyncStorage.getItem(STORAGE_KEYS.CUSTOM_IP);
    return ip;
  } catch (error) {
    console.error('获取自定义IP失败:', error);
    return null;
  }
};

/**
 * 保存JWT Token
 */
export const saveAuthToken = async (token) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    console.log('JWT Token已保存');
    return true;
  } catch (error) {
    console.error('保存Token失败:', error);
    return false;
  }
};

/**
 * 获取JWT Token
 */
export const getAuthToken = async () => {
  try {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    return token;
  } catch (error) {
    console.error('获取Token失败:', error);
    return null;
  }
};

/**
 * 清除JWT Token（登出时使用）
 */
export const clearAuthToken = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    console.log('JWT Token已清除');
    return true;
  } catch (error) {
    console.error('清除Token失败:', error);
    return false;
  }
};

/**
 * 根据平台动态设置API地址
 */
const getBaseUrl = async () => {
  const config = NETWORK_CONFIG.development;
  // 优先使用自定义IP
  const customIP = await getCustomIP();
  if (customIP) {
    return `http://${customIP}:8080/api/auth`;
  }
  if (Platform.OS === 'web') {
    return config.web;
  } else if (Platform.OS === 'android') {
    return config.androidDevice;
  } else if (Platform.OS === 'ios') {
    return config.iosDevice;
  } else {
    return config.ios;
  }
};

// 添加请求超时和错误处理
const fetchWithTimeout = async (url, options, timeout = 10000) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接');
    }
    throw new Error(`网络请求失败: ${error.message}`);
  }
};

/**
 * 测试网络连接
 */
export async function testNetworkConnection() {
  const BASE_URL = await getBaseUrl();
  console.log('测试网络连接:', BASE_URL);
  try {
    const response = await fetchWithTimeout(`${BASE_URL}/test`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    }, 5000);
    console.log('网络连接测试成功');
    return true;
  } catch (error) {
    console.error('网络连接测试失败:', error.message);
    return false;
  }
}

/**
 * 用户注册
 */
export async function register(userAccount, userPassword, checkPassword) {
  const BASE_URL = await getBaseUrl();
  console.log('注册请求地址:', BASE_URL);
  try {
    const response = await fetchWithTimeout(`${BASE_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userAccount, userPassword, checkPassword }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`注册失败：${errorData}`);
    }

    // 检查响应是否有内容
    const responseText = await response.text();
    if (!responseText) {
      // 如果响应为空，返回成功标识
      return { success: true, message: '注册成功' };
    }

    try {
      return JSON.parse(responseText);
    } catch (jsonError) {
      // 如果不是JSON格式，但请求成功，返回成功标识
      console.log('注册成功，但响应不是JSON格式:', responseText);
      return { success: true, message: '注册成功' };
    }
  } catch (error) {
    console.error('注册错误:', error);
    throw error;
  }
}
/**
 * 用户登录
 */
/**
 * 用户登录（自动处理 token 和用户信息）
 */
export async function login(userAccount, userPassword) {
  const BASE_URL = await getBaseUrl();
  console.log('登录请求地址:', BASE_URL);
  try {
    const res = await fetchWithTimeout(`${BASE_URL}/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userAccount, userPassword }),
    });

    console.log('响应状态:', res.status, res.statusText);

    if (!res.ok) {
      const err = await res.text();
      throw new Error(err || '登录失败');
    }

    const responseText = await res.text();
    console.log('登录API原始响应:', responseText);

    if (!responseText || responseText.trim() === '') {
      throw new Error('服务器返回空响应');
    }

    const response = JSON.parse(responseText);
    console.log('登录API解析后数据:', response);

    // ✅ 情况：后端只返回 token
    if (response.token && typeof response.token === 'string') {
      await saveAuthToken(response.token); // 存储 token
      console.log('JWT Token 保存成功，token:', response.token.substring(0, 20) + '...');

      // 验证 token 是否真的保存了
      const savedToken = await getAuthToken();
      console.log('验证保存的 token:', savedToken ? '保存成功' : '保存失败');

      // ⏬ 发起请求获取用户信息
      const userInfo = await getCurrentUser();
      if (userInfo && userInfo.userAccount) {
        console.log('拉取的用户信息:', userInfo);
        return userInfo;
      } else {
        throw new Error('登录成功，但未能获取用户信息');
      }
    }

    // ✅ 兼容原有多种结构
    let userData = null;

    if (response.data && response.data.id && response.data.userAccount) {
      // 🔧 处理 response.data.token 的情况
      if (response.data.token && typeof response.data.token === 'string') {
        await saveAuthToken(response.data.token);
        console.log('JWT Token 保存成功 (从 data.token)，token:', response.data.token.substring(0, 20) + '...');

        // 验证 token 是否真的保存了
        const savedToken = await getAuthToken();
        console.log('验证保存的 token:', savedToken ? '保存成功' : '保存失败');
      }

      userData = {
        id: response.data.id,
        userAccount: response.data.userAccount,
        userName: response.data.userName,
        userAvatar: response.data.userAvatar,
        userProfile: response.data.userProfile,
        userRole: response.data.userRole,
        isCompleted: response.data.isCompleted
      };
    } else if (response.id && response.userAccount) {
      // 🔧 处理 response.token 的情况
      if (response.token && typeof response.token === 'string') {
        await saveAuthToken(response.token);
        console.log('JWT Token 保存成功 (从 response.token)，token:', response.token.substring(0, 20) + '...');
      }

      userData = {
        id: response.id,
        userAccount: response.userAccount,
        userName: response.userName,
        userAvatar: response.userAvatar,
        userProfile: response.userProfile,
        userRole: response.userRole,
        profileSetupCompleted: response.profileSetupCompleted
      };
    } else if (response.user && response.user.id && response.user.userAccount) {
      userData = {
        id: response.user.id,
        userAccount: response.user.userAccount,
        userName: response.user.userName,
        userAvatar: response.user.userAvatar,
        userProfile: response.user.userProfile,
        userRole: response.user.userRole,
        profileSetupCompleted: response.user.profileSetupCompleted
      };
    } else if (response.success || response.code === 0) {
      const possibleUser = response.data || response.user || response;
      if (possibleUser && (possibleUser.id || possibleUser.userAccount)) {
        userData = {
          id: possibleUser.id || possibleUser.userId || Date.now(),
          userAccount: possibleUser.userAccount || possibleUser.account || userAccount,
          userName: possibleUser.userName || possibleUser.name || possibleUser.username,
          userAvatar: possibleUser.userAvatar || possibleUser.avatar,
          userProfile: possibleUser.userProfile || possibleUser.profile,
          userRole: possibleUser.userRole || possibleUser.role || 'user',
          profileSetupCompleted: possibleUser.profileSetupCompleted
        };
      }
    }

    if (userData && userData.id && userData.userAccount) {
      console.log('提取的用户数据:', userData);
      return userData;
    } else {
      console.error('完整响应数据结构:', JSON.stringify(response, null, 2));
      throw new Error('登录响应数据格式错误，缺少必要的用户信息。请检查服务器返回的数据结构。');
    }

  } catch (error) {
    console.error('登录错误:', error);
    throw error;
  }
}

/**
 * 轻量级用户验证（只获取 id 和 userAccount）
 * 使用现有的 /me 端点，但只提取必要的字段
 */
export async function validateUserBasic() {
  const startTime = Date.now();
  const BASE_URL = await getBaseUrl();
  const token = await getAuthToken();

  if (!token) {
    throw new Error('未找到认证token');
  }

  try {
    console.log('开始轻量级用户验证...');
    const res = await fetchWithTimeout(`${BASE_URL}/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
    });

    console.log('轻量级用户验证响应状态:', res.status);

    if (!res.ok) {
      const err = await res.text();
      console.error('轻量级用户验证失败：', err);
      return null;
    }

    const responseText = await res.text();
    if (!responseText || responseText.trim() === '') {
      return null;
    }

    const response = JSON.parse(responseText);
    let basicUserData = null;

    // 只提取 id 和 userAccount，忽略其他字段以减少处理
    if (response.data && (response.data.id || response.data.userAccount)) {
      basicUserData = {
        id: response.data.id,
        userAccount: response.data.userAccount
      };
    } else if (response.id || response.userAccount) {
      basicUserData = {
        id: response.id,
        userAccount: response.userAccount
      };
    } else if (response.user && (response.user.id || response.user.userAccount)) {
      basicUserData = {
        id: response.user.id,
        userAccount: response.user.userAccount
      };
    }

    if (basicUserData && (basicUserData.id || basicUserData.userAccount)) {
      const endTime = Date.now();
      console.log(`轻量级用户验证成功 (仅提取 id 和 userAccount，耗时 ${endTime - startTime}ms):`, basicUserData);
      return basicUserData;
    } else {
      console.error('轻量级用户验证数据格式无效:', response);
      return null;
    }
  } catch (error) {
    const endTime = Date.now();
    console.error(`轻量级用户验证错误 (耗时 ${endTime - startTime}ms):`, error);
    return null;
  }
}

/**
 * 获取当前用户信息（通过 userAccount 查询）
 */
export async function getCurrentUser() {
  const BASE_URL = await getBaseUrl();
  const token = await getAuthToken();

  if (!token) {
    throw new Error('未找到认证token');
  }

  try {
    const res = await fetchWithTimeout(`${BASE_URL}/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}` // ✅ 添加JWT
      },
    });

    console.log('获取用户信息响应状态:', res.status);

    if (!res.ok) {
      const err = await res.text();
      console.error('获取用户失败：', err);
      return null;
    }

    const responseText = await res.text();
    if (!responseText || responseText.trim() === '') {
      return null;
    }

    const response = JSON.parse(responseText);
    let userData = null;

    if (response.data && response.data.id && response.data.userAccount) {
      userData = response.data;
    } else if (response.id && response.userAccount) {
      userData = response;
    } else if (response.user && response.user.id && response.user.userAccount) {
      userData = response.user;
    }

    if (userData) {
      return userData;
    } else {
      console.error('用户数据格式无效:', response);
      return null;
    }
  } catch (error) {
    console.error('获取用户信息错误:', error);
    return null;
  }
}

/**
 * 验证JWT Token是否有效
 */
export async function validateToken() {
  // 注释掉token验证逻辑，直接返回true保持登录状态
  return true;

  // const token = await getAuthToken();
  // if (!token) return false;

  // const BASE_URL = await getBaseUrl();
  // try {
  //   const res = await fetchWithTimeout(`${BASE_URL}/validate`, {
  //     method: 'GET',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': `Bearer ${token}`
  //     },
  //   });
  //   return res.ok;
  // } catch (error) {
  //   console.error('验证Token失败:', error);
  //   return false;
  // }
}

/**
 * 重置网络配置
 */
export async function resetNetworkConfig() {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.CUSTOM_IP);
    console.log('网络配置已重置');
    return true;
  } catch (error) {
    console.error('重置网络配置失败:', error);
    return false;
  }
}

/**
 * 根据步骤ID获取标签列表
 */
export async function getTagsByStepId(stepId) {
  const BASE_URL = await getBaseUrl();
  console.log('获取标签请求地址:', `${BASE_URL}/tag/step/${stepId}`);
  try {
    const response = await fetchWithTimeout(`${BASE_URL}/tag/step/${stepId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`获取标签失败：${errorData}`);
    }

    const responseText = await response.text();
    if (!responseText) {
      return [];
    }

    try {
      const result = JSON.parse(responseText);

      // 处理不同的响应格式
      let tags = [];
      if (Array.isArray(result)) {
        tags = result;
      } else if (result.data && Array.isArray(result.data)) {
        tags = result.data;
      } else if (result.success && result.data && Array.isArray(result.data)) {
        tags = result.data;
      }

      // 转换为前端需要的格式
      return tags.map(tag => ({
        id: tag.tag_id || tag.tagId || tag.id,
        text: tag.tag_text || tag.tagText || tag.text,
        color: tag.tag_color || tag.tagColor || tag.color || '#FF6B35'
      }));

    } catch (jsonError) {
      console.error('标签数据JSON解析错误:', jsonError.message);
      return [];
    }
  } catch (error) {
    console.error('获取标签错误:', error);
    throw error;
  }
}


