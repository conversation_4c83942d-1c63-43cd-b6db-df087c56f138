// lib/backendCheck.js
import { Platform } from 'react-native';

// 检查后端服务状态
export const checkBackendStatus = async (baseUrl = 'http://*************:8080') => {
  console.log(`[${Platform.OS}] 开始检查后端服务状态...`);
  
  const checks = [
    {
      name: '基础连接',
      url: baseUrl,
      description: '检查服务器是否响应'
    },
    {
      name: '健康检查',
      url: `${baseUrl}/health`,
      description: '检查健康检查端点'
    },
    {
      name: 'API根路径',
      url: `${baseUrl}/api`,
      description: '检查API根路径'
    },
    {
      name: 'CORS预检',
      url: `${baseUrl}/api/index`,
      method: 'OPTIONS',
      description: '检查CORS配置'
    }
  ];

  const results = [];

  for (const check of checks) {
    try {
      console.log(`[${Platform.OS}] 检查: ${check.name} - ${check.description}`);
      console.log(`[${Platform.OS}] URL: ${check.url}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
      
      const startTime = Date.now();
      const response = await fetch(check.url, {
        method: check.method || 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:3000', // 模拟前端请求
        }
      });
      
      clearTimeout(timeoutId);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        name: check.name,
        url: check.url,
        success: true,
        status: response.status,
        statusText: response.statusText,
        duration: `${duration}ms`,
        headers: Object.fromEntries(response.headers.entries()),
        description: check.description
      };
      
      results.push(result);
      console.log(`[${Platform.OS}] ✅ ${check.name} 成功:`, {
        status: result.status,
        duration: result.duration
      });
      
    } catch (error) {
      const result = {
        name: check.name,
        url: check.url,
        success: false,
        error: error.message,
        errorType: error.name,
        description: check.description
      };
      
      results.push(result);
      console.log(`[${Platform.OS}] ❌ ${check.name} 失败:`, {
        error: error.message,
        type: error.name
      });
    }
  }

  // 分析结果
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`[${Platform.OS}] 后端检查完成:`, {
    total: totalCount,
    success: successCount,
    failed: totalCount - successCount,
    successRate: `${Math.round((successCount / totalCount) * 100)}%`
  });

  // 提供建议
  if (successCount === 0) {
    console.error(`[${Platform.OS}] 🚨 所有检查都失败了！`);
    console.error('可能的原因：');
    console.error('1. 后端服务没有运行');
    console.error('2. IP地址不正确');
    console.error('3. 端口被防火墙阻止');
    console.error('4. 网络连接问题');
  } else if (successCount < totalCount) {
    console.warn(`[${Platform.OS}] ⚠️ 部分检查失败`);
    console.warn('可能的原因：');
    console.warn('1. 某些API端点不存在');
    console.warn('2. CORS配置问题');
    console.warn('3. 认证要求');
  } else {
    console.log(`[${Platform.OS}] ✅ 所有检查都通过了！`);
  }

  return {
    results,
    summary: {
      total: totalCount,
      success: successCount,
      failed: totalCount - successCount,
      successRate: Math.round((successCount / totalCount) * 100)
    }
  };
};

// 快速ping测试
export const quickPing = async (host = '*************', port = 8080) => {
  const url = `http://${host}:${port}`;
  console.log(`[${Platform.OS}] 快速ping测试: ${url}`);
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
    
    const startTime = Date.now();
    const response = await fetch(url, {
      method: 'HEAD', // 只获取头部，减少数据传输
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`[${Platform.OS}] ✅ Ping成功: ${duration}ms`);
    return { success: true, duration, status: response.status };
    
  } catch (error) {
    console.log(`[${Platform.OS}] ❌ Ping失败: ${error.message}`);
    return { success: false, error: error.message };
  }
};

// 网络诊断
export const diagnoseNetwork = async () => {
  console.log(`[${Platform.OS}] 开始网络诊断...`);
  
  // 1. 快速ping测试
  const pingResult = await quickPing();
  
  // 2. 完整后端检查
  const backendResult = await checkBackendStatus();
  
  // 3. 生成诊断报告
  const diagnosis = {
    ping: pingResult,
    backend: backendResult,
    recommendations: []
  };
  
  if (!pingResult.success) {
    diagnosis.recommendations.push('基础连接失败，检查IP地址和端口');
    diagnosis.recommendations.push('确认后端服务正在运行');
    diagnosis.recommendations.push('检查防火墙设置');
  } else if (backendResult.summary.success === 0) {
    diagnosis.recommendations.push('服务器响应但API不可用');
    diagnosis.recommendations.push('检查后端API路由配置');
    diagnosis.recommendations.push('查看后端服务日志');
  } else if (backendResult.summary.successRate < 100) {
    diagnosis.recommendations.push('部分API可用，检查具体失败的端点');
    diagnosis.recommendations.push('可能需要配置CORS或认证');
  } else {
    diagnosis.recommendations.push('网络连接正常！');
  }
  
  console.log(`[${Platform.OS}] 诊断完成:`, diagnosis);
  return diagnosis;
};
