import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// 与api.js保持一致的存储键名
const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
};

export const fetchWithAuth = async (url, options = {}, retryCount = 0) => {
  const maxRetries = 2;
  const timeoutMs = 15000; // 15秒超时

  try {
    // 添加网络调试信息
    console.log(`[${Platform.OS}] 发起网络请求 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, {
      url,
      method: options.method || 'GET',
      platform: Platform.OS,
      timeout: `${timeoutMs}ms`
    });

    const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

    const headers = {
      ...options.headers,
      "Content-Type": "application/json",
    };

    // 只有在token存在时才添加Authorization头
    if (token) {
      headers.Authorization = `Bearer ${token}`;
      console.log(`[${Platform.OS}] 使用认证token`);
    } else {
      console.log(`[${Platform.OS}] 未找到认证token`);
    }

    console.log(`[${Platform.OS}] 请求头:`, headers);

    // 创建超时控制器
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.log(`[${Platform.OS}] 请求超时，取消请求`);
      controller.abort();
    }, timeoutMs);

    const startTime = Date.now();

    const response = await fetch(url, {
      ...options,
      headers,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[${Platform.OS}] 响应成功 (耗时 ${duration}ms):`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    });

    // 如果返回401未授权，可能需要重新登录
    if (response.status === 401) {
      console.warn(`[${Platform.OS}] Token可能已过期，需要重新登录`);
      // 可以在这里添加自动登出逻辑
      // await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    }

    return response;
  } catch (error) {
    console.error(`[${Platform.OS}] fetchWithAuth错误 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, {
      message: error.message,
      name: error.name,
      url,
      retryCount
    });

    // 检查是否应该重试
    const shouldRetry = retryCount < maxRetries && (
      error.name === 'AbortError' || // 超时
      error.message.includes('Network request failed') ||
      error.message.includes('timeout') ||
      error.message.includes('fetch')
    );

    if (shouldRetry) {
      console.log(`[${Platform.OS}] 准备重试请求，等待 ${(retryCount + 1) * 1000}ms...`);
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
      return fetchWithAuth(url, options, retryCount + 1);
    }

    // 添加网络连接错误的特殊处理
    if (error.message.includes('Network request failed') ||
        error.message.includes('fetch') ||
        error.message.includes('timeout') ||
        error.name === 'AbortError') {
      console.error(`[${Platform.OS}] 网络连接问题，请检查：`);
      console.error('1. 后端服务是否正在运行 (检查端口 8080)');
      console.error('2. IP地址是否正确 (当前: **************)');
      console.error('3. 手机和开发机器是否在同一WiFi网络');
      console.error('4. 防火墙是否阻止了连接');
      console.error('5. 后端服务是否监听 0.0.0.0:8080 而不是 localhost:8080');
    }

    throw error;
  }
};
