import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// 与api.js保持一致的存储键名
const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
};

export const fetchWithAuth = async (url, options = {}) => {
  try {
    // 添加网络调试信息
    console.log(`[${Platform.OS}] 发起网络请求:`, {
      url,
      method: options.method || 'GET',
      platform: Platform.OS
    });

    const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

    const headers = {
      ...options.headers,
      "Content-Type": "application/json",
    };

    // 只有在token存在时才添加Authorization头
    if (token) {
      headers.Authorization = `Bearer ${token}`;
      console.log(`[${Platform.OS}] 使用认证token`);
    } else {
      console.log(`[${Platform.OS}] 未找到认证token`);
    }

    console.log(`[${Platform.OS}] 请求头:`, headers);

    const response = await fetch(url, {
      ...options,
      headers,
    });

    console.log(`[${Platform.OS}] 响应状态:`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    // 如果返回401未授权，可能需要重新登录
    if (response.status === 401) {
      console.warn(`[${Platform.OS}] Token可能已过期，需要重新登录`);
      // 可以在这里添加自动登出逻辑
      // await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    }

    return response;
  } catch (error) {
    console.error(`[${Platform.OS}] fetchWithAuth错误:`, {
      message: error.message,
      url,
      error
    });

    // 添加网络连接错误的特殊处理
    if (error.message.includes('Network request failed') ||
        error.message.includes('fetch')) {
      console.error(`[${Platform.OS}] 网络连接失败，请检查：`);
      console.error('1. 后端服务是否正在运行');
      console.error('2. IP地址是否正确');
      console.error('3. 手机和开发机器是否在同一网络');
      console.error('4. 防火墙是否阻止了连接');
    }

    throw error;
  }
};
