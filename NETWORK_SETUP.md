# 网络配置指南

## 问题描述
Web端可以调用后端接口，但手机端无法调用。这是因为手机端无法访问 `localhost:8080`。

## 解决方案

### 1. 获取开发机器的IP地址

#### Windows:
```bash
ipconfig
```
查找 "无线局域网适配器 WLAN" 或 "以太网适配器"，找到 "IPv4 地址"
例如: `*************`

#### Mac:
```bash
ifconfig
```
查找 `en0` 或 `en1` 接口，找到 `inet` 行
例如: `inet *************`

#### Linux:
```bash
ip addr show
# 或
ifconfig
```
查找活动的网络接口，找到 inet 地址
例如: `*************/24`

### 2. 更新配置文件

编辑 `constants/index.js` 文件，将 `getDevMachineIP()` 函数中的IP地址替换为您的实际IP：

```javascript
const getDevMachineIP = () => {
  return '*************'; // 替换为您的实际IP地址
};
```

### 3. 验证配置

1. 确保后端服务正在运行在端口 8080
2. 确保手机和开发机器连接到同一个WiFi网络
3. 在手机浏览器中测试访问: `http://[您的IP]:8080`
4. 检查防火墙是否允许端口 8080 的连接

### 4. 常见问题排查

#### 问题1: 无法获取IP地址
- 确保网络适配器已启用
- 检查是否连接到网络
- 尝试重启网络适配器

#### 问题2: 手机无法访问IP
- 确认手机和电脑在同一WiFi网络
- 检查路由器是否启用了AP隔离
- 尝试关闭防火墙测试

#### 问题3: 端口被阻止
- 检查Windows防火墙设置
- 检查杀毒软件是否阻止连接
- 尝试使用其他端口

### 5. 测试工具

应用中包含了网络测试工具：
- 使用 `NetworkTestButton` 组件测试连接
- 查看控制台输出的详细调试信息
- 使用网络帮助功能获取配置指导

### 6. 开发环境配置

确保您的开发环境配置正确：

1. **Expo CLI**: 确保使用最新版本
2. **后端服务**: 确保监听所有接口 (0.0.0.0:8080)，而不仅仅是 localhost
3. **CORS设置**: 确保后端允许来自手机IP的跨域请求

### 7. 后端服务配置

如果您的后端服务只监听 localhost，需要修改为监听所有接口：

```javascript
// 不要这样做
app.listen(8080, 'localhost');

// 应该这样做
app.listen(8080, '0.0.0.0');
```

### 8. 调试步骤

1. 检查控制台输出，查看详细的网络请求日志
2. 使用网络测试按钮验证连接
3. 在手机浏览器中直接访问API端点
4. 检查后端服务日志，确认是否收到请求

## 快速检查清单

- [ ] 获取了正确的开发机器IP地址
- [ ] 更新了 `constants/index.js` 中的IP配置
- [ ] 后端服务正在运行并监听 0.0.0.0:8080
- [ ] 手机和开发机器在同一WiFi网络
- [ ] 防火墙允许端口 8080 的连接
- [ ] 在手机浏览器中可以访问 `http://[IP]:8080`
- [ ] 使用网络测试工具验证连接

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 开发机器的操作系统
2. 网络配置信息
3. 控制台错误日志
4. 网络测试结果
