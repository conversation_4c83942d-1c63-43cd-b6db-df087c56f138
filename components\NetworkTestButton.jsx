// components/NetworkTestButton.jsx
import React, { useState } from 'react';
import { View, TouchableOpacity, Alert, Platform } from 'react-native';
import ThemedText from './ThemedText';
import { testNetworkConnection, getIPAddressInstructions, troubleshootNetwork } from '../lib/networkTest';
import { API_BASE_URL, getDevIP } from '../constants/index';

const NetworkTestButton = ({ style }) => {
  const [testing, setTesting] = useState(false);

  const runNetworkTest = async () => {
    if (testing) return;
    
    setTesting(true);
    
    try {
      console.log('开始网络连接测试...');
      
      // 显示当前配置信息
      const configInfo = `
平台: ${Platform.OS}
API基础URL: ${API_BASE_URL}
${Platform.OS !== 'web' ? `开发机器IP: ${getDevIP()}` : '使用localhost (Web端)'}
      `;
      
      console.log('当前配置:', configInfo);
      
      // 运行网络测试
      const results = await testNetworkConnection();
      
      // 分析结果
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      
      let message = `网络测试完成\n\n`;
      message += `成功: ${successCount}/${totalCount}\n\n`;
      
      if (successCount === totalCount) {
        message += '✅ 所有测试通过！网络连接正常。';
      } else {
        message += '❌ 部分测试失败。\n\n';
        message += '失败的测试:\n';
        results.filter(r => !r.success).forEach(result => {
          message += `• ${result.name}: ${result.error}\n`;
        });
        
        message += '\n请检查:\n';
        message += '1. 后端服务是否运行\n';
        message += '2. IP地址是否正确\n';
        message += '3. 网络连接是否正常';
      }
      
      Alert.alert('网络测试结果', message);
      
    } catch (error) {
      console.error('网络测试失败:', error);
      Alert.alert(
        '网络测试失败', 
        `测试过程中出现错误:\n${error.message}\n\n请检查网络配置。`
      );
    } finally {
      setTesting(false);
    }
  };

  const showNetworkHelp = () => {
    const helpMessage = `
网络配置帮助

当前配置:
• 平台: ${Platform.OS}
• API URL: ${API_BASE_URL}
${Platform.OS !== 'web' ? `• 开发机器IP: ${getDevIP()}` : '• 使用localhost (Web端)'}

如果手机端无法连接:
1. 确认后端服务正在运行
2. 获取开发机器的实际IP地址
3. 更新 constants/index.js 中的IP
4. 确保手机和电脑在同一WiFi
5. 检查防火墙设置

获取IP地址方法:
• Windows: 运行 ipconfig
• Mac: 运行 ifconfig
• Linux: 运行 ip addr show
    `;
    
    Alert.alert('网络配置帮助', helpMessage);
    
    // 同时在控制台输出详细说明
    getIPAddressInstructions();
    troubleshootNetwork();
  };

  return (
    <View style={[{ flexDirection: 'row', gap: 10 }, style]}>
      <TouchableOpacity
        onPress={runNetworkTest}
        disabled={testing}
        style={{
          backgroundColor: testing ? '#ccc' : '#007AFF',
          paddingHorizontal: 15,
          paddingVertical: 8,
          borderRadius: 8,
          flex: 1
        }}
      >
        <ThemedText style={{ 
          color: 'white', 
          textAlign: 'center',
          fontSize: 14
        }}>
          {testing ? '测试中...' : '测试网络连接'}
        </ThemedText>
      </TouchableOpacity>
      
      <TouchableOpacity
        onPress={showNetworkHelp}
        style={{
          backgroundColor: '#34C759',
          paddingHorizontal: 15,
          paddingVertical: 8,
          borderRadius: 8,
          flex: 1
        }}
      >
        <ThemedText style={{ 
          color: 'white', 
          textAlign: 'center',
          fontSize: 14
        }}>
          网络帮助
        </ThemedText>
      </TouchableOpacity>
    </View>
  );
};

export default NetworkTestButton;
