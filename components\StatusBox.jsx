import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet
} from 'react-native';

const StatusBox = ({
  type = 'success', // or 'error'
  title = 'Success!',
  message = 'Everything is working.',
  buttonText = 'Continue',
  onPress = () => { }
}) => {
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const rollAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (type === 'success') {
      Animated.loop(
        Animated.sequence([
          Animated.timing(bounceAnim, {
            toValue: -10,
            duration: 500,
            useNativeDriver: true
          }),
          Animated.timing(bounceAnim, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true
          })
        ])
      ).start();
    } else {
      Animated.loop(
        Animated.sequence([
          Animated.timing(rollAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true
          }),
          Animated.timing(rollAnim, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: true
          })
        ])
      ).start();
    }
  }, [type]);

  const rollInterpolation = rollAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '168deg']
  });

  const isSuccess = type === 'success';

  return (
    <View style={[styles.box, isSuccess ? styles.successBox : styles.errorBox]}>
      {/* 顶部圆点 */}
      <View style={styles.dot} />
      <View style={[styles.dot, styles.dotTwo]} />

      {/* 表情脸 */}
      <Animated.View
        style={[
          styles.face,
          isSuccess
            ? { transform: [{ translateY: bounceAnim }] }
            : { transform: [{ rotate: rollInterpolation }] }
        ]}
      >
        <View style={styles.eye} />
        <View style={[styles.eye, styles.eyeRight]} />
        <View
          style={[
            styles.mouth,
            isSuccess ? styles.happy : styles.sad
          ]}
        />
      </Animated.View>

      {/* 阴影 */}
      <View style={styles.shadow} />

      {/* 文本信息 */}
      <View style={styles.message}>
        <Text style={styles.alert}>{title}</Text>
        <Text style={styles.paragraph}>{message}</Text>
      </View>

      {/* 按钮 */}
      <TouchableOpacity style={styles.buttonBox} onPress={onPress}>
        <Text
          style={[
            styles.buttonText,
            isSuccess ? styles.green : styles.red
          ]}
        >
          {buttonText}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default StatusBox;

const styles = StyleSheet.create({
  box: {
    width: '90%',
    height: 250,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginVertical: 20,
    shadowColor: '#CBCDD3',
    shadowOffset: { width: 5, height: 5 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  successBox: {
    backgroundColor: '#B0DB7D',
  },
  errorBox: {
    backgroundColor: '#EF8D9C',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FCFCFC',
    position: 'absolute',
    top: 10,
    right: 10,
  },
  dotTwo: {
    right: 20,
    opacity: 0.5,
  },
  face: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FCFCFC',
    borderWidth: 1,
    borderColor: '#777',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginVertical: 20,
  },
  eye: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
    backgroundColor: '#777',
    position: 'absolute',
    top: '30%',
    left: '20%',
  },
  eyeRight: {
    left: '70%',
  },
  mouth: {
    width: 7,
    height: 7,
    borderRadius: 3.5,
    position: 'absolute',
  },
  happy: {
    borderWidth: 2,
    borderColor: 'transparent #777 #777 transparent',
    transform: [{ rotate: '45deg' }],
    top: '50%',
    left: '45%',
  },
  sad: {
    borderWidth: 2,
    borderColor: '#777 transparent transparent #777',
    transform: [{ rotate: '45deg' }],
    top: '60%',
    left: '45%',
  },
  shadow: {
    width: 40,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#777',
    opacity: 0.5,
    position: 'absolute',
    bottom: 80,
  },
  message: {
    alignItems: 'center',
    paddingTop: 10,
  },
  alert: {
    fontSize: 14,
    fontWeight: '700',
    color: '#fff',
    letterSpacing: 2,
  },
  paragraph: {
    fontSize: 10,
    color: '#555',
    textTransform: 'none',
  },
  buttonBox: {
    backgroundColor: '#fff',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginTop: 10,
    shadowColor: '#777',
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 4,
  },
  buttonText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  green: {
    color: '#558d6f',
  },
  red: {
    color: '#d55b6c',
  },
});
