// lib/authUtils.js - 认证工具函数
import { Platform } from 'react-native';
import apiServices from './apiServices';

/**
 * 检查用户是否已登录
 * @returns {Promise<{isLoggedIn: boolean, user: object|null, token: string|null}>}
 */
export const checkLoginStatus = async () => {
  try {
    console.log(`[${Platform.OS}] 检查用户登录状态`);
    
    // 1. 检查是否有token
    const token = await apiServices.auth.validateToken();
    if (!token) {
      console.log(`[${Platform.OS}] 未找到认证token`);
      return { isLoggedIn: false, user: null, token: null };
    }
    
    // 2. 验证token是否有效
    try {
      const user = await apiServices.auth.validateUserBasic();
      if (user && (user.id || user.userAccount)) {
        console.log(`[${Platform.OS}] 用户已登录:`, user);
        return { isLoggedIn: true, user, token };
      } else {
        console.log(`[${Platform.OS}] Token无效或用户信息不完整`);
        return { isLoggedIn: false, user: null, token: null };
      }
    } catch (error) {
      console.log(`[${Platform.OS}] Token验证失败:`, error.message);
      return { isLoggedIn: false, user: null, token: null };
    }
  } catch (error) {
    console.error(`[${Platform.OS}] 检查登录状态失败:`, error);
    return { isLoggedIn: false, user: null, token: null };
  }
};

/**
 * 确保用户已登录，如果没有登录则抛出错误
 * @param {string} action - 需要登录的操作描述
 * @throws {Error} 如果用户未登录
 * @returns {Promise<object>} 用户信息
 */
export const requireLogin = async (action = '执行此操作') => {
  const { isLoggedIn, user } = await checkLoginStatus();
  
  if (!isLoggedIn) {
    throw new Error(`请先登录后再${action}`);
  }
  
  return user;
};

/**
 * 安全执行需要登录的操作
 * @param {Function} operation - 需要执行的操作
 * @param {string} actionName - 操作名称
 * @param {Function} onLoginRequired - 需要登录时的回调
 * @returns {Promise<any>} 操作结果
 */
export const executeWithLogin = async (operation, actionName = '操作', onLoginRequired = null) => {
  try {
    // 检查登录状态
    const user = await requireLogin(actionName);
    
    // 执行操作
    return await operation(user);
  } catch (error) {
    if (error.message.includes('登录')) {
      console.log(`[${Platform.OS}] ${actionName}需要登录:`, error.message);
      if (onLoginRequired) {
        onLoginRequired(error);
      }
      throw error;
    } else {
      console.error(`[${Platform.OS}] ${actionName}执行失败:`, error);
      throw error;
    }
  }
};

/**
 * 获取用户ID（如果已登录）
 * @returns {Promise<string|null>} 用户ID或null
 */
export const getUserId = async () => {
  try {
    const { isLoggedIn, user } = await checkLoginStatus();
    return isLoggedIn ? user?.id : null;
  } catch (error) {
    console.error(`[${Platform.OS}] 获取用户ID失败:`, error);
    return null;
  }
};

/**
 * 检查是否为游客模式
 * @returns {Promise<boolean>} 是否为游客模式
 */
export const isGuestMode = async () => {
  const { isLoggedIn } = await checkLoginStatus();
  return !isLoggedIn;
};

/**
 * 清除登录状态
 * @returns {Promise<boolean>} 是否成功清除
 */
export const clearLoginStatus = async () => {
  try {
    await apiServices.auth.logout();
    console.log(`[${Platform.OS}] 登录状态已清除`);
    return true;
  } catch (error) {
    console.error(`[${Platform.OS}] 清除登录状态失败:`, error);
    return false;
  }
};

/**
 * 格式化登录状态信息（用于调试）
 * @returns {Promise<string>} 格式化的状态信息
 */
export const getLoginStatusInfo = async () => {
  const { isLoggedIn, user, token } = await checkLoginStatus();
  
  return `登录状态: ${isLoggedIn ? '已登录' : '未登录'}
用户信息: ${user ? `${user.userAccount} (ID: ${user.id})` : '无'}
Token状态: ${token ? '有效' : '无效'}
平台: ${Platform.OS}`;
};

/**
 * 登录状态变化监听器
 */
export class LoginStatusListener {
  constructor() {
    this.listeners = [];
  }
  
  /**
   * 添加监听器
   * @param {Function} callback - 状态变化回调
   */
  addListener(callback) {
    this.listeners.push(callback);
  }
  
  /**
   * 移除监听器
   * @param {Function} callback - 要移除的回调
   */
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }
  
  /**
   * 通知所有监听器
   * @param {object} status - 登录状态
   */
  notifyListeners(status) {
    this.listeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error(`[${Platform.OS}] 登录状态监听器错误:`, error);
      }
    });
  }
  
  /**
   * 检查并通知状态变化
   */
  async checkAndNotify() {
    const status = await checkLoginStatus();
    this.notifyListeners(status);
    return status;
  }
}

// 创建全局登录状态监听器实例
export const loginStatusListener = new LoginStatusListener();
