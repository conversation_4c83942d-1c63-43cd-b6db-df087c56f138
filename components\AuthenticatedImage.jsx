// components/AuthenticatedImage.jsx - 支持认证的图片组件
import React, { useState, useEffect } from 'react';
import { Image, View, Platform } from 'react-native';
import { createAuthenticatedImageUrl, isImageAuthRequired, transformImageUrl } from '../lib/imageUtils';
import ThemedText from './ThemedText';

const AuthenticatedImage = ({ 
  source, 
  style, 
  placeholder = 'https://via.placeholder.com/300x200?text=No+Image',
  showDebugInfo = false,
  requireAuth = true, // 是否需要认证
  onError,
  onLoad,
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [processedUrl, setProcessedUrl] = useState(null);
  const [debugInfo, setDebugInfo] = useState('');

  // 处理图片URL
  useEffect(() => {
    const processImageUrl = async () => {
      if (!source) {
        setProcessedUrl(placeholder);
        setDebugInfo('无源图片，使用占位符');
        return;
      }

      let originalUrl = '';
      if (typeof source === 'string') {
        originalUrl = source;
      } else if (source?.uri) {
        originalUrl = source.uri;
      } else {
        setProcessedUrl(placeholder);
        setDebugInfo('无效的图片源，使用占位符');
        return;
      }

      try {
        setDebugInfo(`原始URL: ${originalUrl}`);
        
        // 检查是否需要认证
        if (requireAuth && isImageAuthRequired(originalUrl)) {
          console.log(`[${Platform.OS}] 图片需要认证:`, originalUrl);
          const authUrl = await createAuthenticatedImageUrl(originalUrl);
          setProcessedUrl(authUrl);
          setDebugInfo(prev => prev + `\n需要认证，已添加token`);
        } else {
          // 不需要认证，只做URL转换
          const transformedUrl = transformImageUrl(originalUrl);
          setProcessedUrl(transformedUrl);
          setDebugInfo(prev => prev + `\n不需要认证，仅转换URL`);
        }
      } catch (error) {
        console.error(`[${Platform.OS}] 图片URL处理失败:`, error);
        setProcessedUrl(placeholder);
        setDebugInfo(`处理失败: ${error.message}`);
      }
    };

    processImageUrl();
  }, [source, requireAuth, placeholder]);

  // 获取最终的图片源
  const getImageSource = () => {
    if (!processedUrl) return { uri: placeholder };
    
    if (typeof source === 'object' && source.uri) {
      return { ...source, uri: processedUrl };
    }
    
    return { uri: processedUrl };
  };

  const handleError = (error) => {
    console.error(`[${Platform.OS}] 认证图片加载失败:`, {
      originalSource: source,
      processedUrl: processedUrl,
      error: error.nativeEvent?.error || error
    });
    
    setImageError(true);
    setIsLoading(false);
    
    if (onError) {
      onError(error);
    }
  };

  const handleLoad = (event) => {
    console.log(`[${Platform.OS}] 认证图片加载成功:`, processedUrl);
    setIsLoading(false);
    setImageError(false);
    
    if (onLoad) {
      onLoad(event);
    }
  };

  const handleLoadStart = () => {
    setIsLoading(true);
    setImageError(false);
  };

  // 如果处理中，显示加载状态
  if (!processedUrl) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }]}>
        <ThemedText style={{ fontSize: 12, color: '#666' }}>处理中...</ThemedText>
      </View>
    );
  }

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }]}>
        <ThemedText style={{ fontSize: 12, color: '#666' }}>图片加载失败</ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ fontSize: 10, color: '#999', marginTop: 5 }}>
            {debugInfo}
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={getImageSource()}
        style={[{ width: '100%', height: '100%' }, style]}
        onError={handleError}
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        {...props}
      />
      
      {/* 调试信息 */}
      {showDebugInfo && (
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          padding: 5
        }}>
          <ThemedText style={{ fontSize: 10, color: 'white' }}>
            {debugInfo}
          </ThemedText>
          <ThemedText style={{ fontSize: 9, color: '#ccc' }}>
            最终URL: {processedUrl?.substring(0, 50)}...
          </ThemedText>
        </View>
      )}
      
      {/* 加载指示器 */}
      {isLoading && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255,255,255,0.8)'
        }}>
          <ThemedText style={{ fontSize: 12, color: '#666' }}>加载中...</ThemedText>
        </View>
      )}
    </View>
  );
};

export default AuthenticatedImage;
