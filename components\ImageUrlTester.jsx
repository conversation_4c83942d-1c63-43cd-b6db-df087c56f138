// components/ImageUrlTester.jsx - 图片URL处理测试工具
import React, { useState } from 'react';
import { View, TouchableOpacity, ScrollView, Platform } from 'react-native';
import ThemedText from './ThemedText';
import ThemedTextInput from './ThemedTextInput';
import SafeImage from './SafeImage';
import CleanImage from './CleanImage';
import { transformImageUrl } from '../lib/imageUtils';

const ImageUrlTester = () => {
  const [testImageUrl, setTestImageUrl] = useState('http://localhost:8080/images/tag 10.png');
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState(null);

  // 测试图片URL处理
  const testImageUrl = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      console.log(`[${Platform.OS}] 开始测试图片URL处理`);

      // 测试URL转换
      const transformedUrl = transformImageUrl(testImageUrl);

      setTestResults({
        originalUrl: testImageUrl,
        transformedUrl: transformedUrl,
        hasSpaces: testImageUrl.includes(' '),
        hasLocalhost: testImageUrl.includes('localhost'),
        platform: Platform.OS
      });

      console.log(`[${Platform.OS}] 图片URL测试完成:`, {
        original: testImageUrl,
        transformed: transformedUrl
      });

    } catch (error) {
      console.error(`[${Platform.OS}] 图片URL测试失败:`, error);
      setTestResults({
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  // 常见的测试图片URL（包含格式问题的URL）
  const commonTestUrls = [
    'http://localhost:8080/images/tag 10.png',  // 有空格
    'http://localhost:8080/images/tag  13  png', // 多空格
    'http://localhost:8080/images/tag//35.png',  // 多斜杠
    'http://localhost:8080/images/tag_2.png',    // 正常格式
    'http://localhost:8080/images/tag 41.png',   // 有空格
    'http://localhost:8080/images/tag_5.png'     // 正常格式
  ];

  return (
    <ScrollView style={{ padding: 20, backgroundColor: '#f8f9fa' }}>
      <ThemedText style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        图片URL处理测试工具
      </ThemedText>

      {/* 测试URL输入 */}
      <View style={{ marginBottom: 20 }}>
        <ThemedText style={{ marginBottom: 5 }}>测试图片URL:</ThemedText>
        <ThemedTextInput
          value={testImageUrl}
          onChangeText={setTestImageUrl}
          placeholder="输入图片URL"
          style={{ marginBottom: 10 }}
        />
        
        {/* 快速选择常见URL */}
        <ThemedText style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
          快速选择:
        </ThemedText>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
          {commonTestUrls.map((url, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => setTestImageUrl(url)}
              style={{
                backgroundColor: '#e9ecef',
                padding: 5,
                borderRadius: 3,
                margin: 2
              }}
            >
              <ThemedText style={{ fontSize: 10 }}>
                tag_{url.match(/tag_?(\d+)/)?.[1] || index + 1}.png
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* 测试按钮 */}
      <TouchableOpacity
        onPress={testImageUrl}
        disabled={loading}
        style={[
          {
            backgroundColor: loading ? '#6c757d' : '#007bff',
            padding: 15,
            borderRadius: 8,
            alignItems: 'center',
            marginBottom: 20
          }
        ]}
      >
        <ThemedText style={{ color: 'white', fontWeight: 'bold' }}>
          {loading ? '测试中...' : '测试URL处理'}
        </ThemedText>
      </TouchableOpacity>



      {/* 测试结果 */}
      {testResults && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 15,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#dee2e6',
          marginBottom: 20
        }}>
          <ThemedText style={{ fontWeight: 'bold', marginBottom: 10 }}>
            测试结果:
          </ThemedText>
          
          {testResults.error ? (
            <ThemedText style={{ color: '#dc3545' }}>
              错误: {testResults.error}
            </ThemedText>
          ) : (
            <View>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                原始URL: {testResults.originalUrl}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                转换后URL: {testResults.transformedUrl}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                包含空格: {testResults.hasSpaces ? '是' : '否'}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                包含localhost: {testResults.hasLocalhost ? '是' : '否'}
              </ThemedText>
              <ThemedText style={{ fontSize: 12, marginBottom: 5 }}>
                当前平台: {testResults.platform}
              </ThemedText>
            </View>
          )}
        </View>
      )}

      {/* 图片对比测试 */}
      {testResults && !testResults.error && (
        <View style={{ marginBottom: 20 }}>
          <ThemedText style={{ fontWeight: 'bold', marginBottom: 10 }}>
            图片加载对比:
          </ThemedText>
          
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            {/* 普通SafeImage */}
            <View style={{ flex: 1, marginRight: 10 }}>
              <ThemedText style={{ fontSize: 12, marginBottom: 5, textAlign: 'center' }}>
                SafeImage (原始处理)
              </ThemedText>
              <SafeImage
                source={testImageUrl}
                style={{ width: '100%', height: 100, backgroundColor: '#f0f0f0' }}
                showDebugInfo={true}
              />
            </View>

            {/* CleanImage */}
            <View style={{ flex: 1, marginLeft: 10 }}>
              <ThemedText style={{ fontSize: 12, marginBottom: 5, textAlign: 'center' }}>
                CleanImage (修复URL格式)
              </ThemedText>
              <CleanImage
                source={testImageUrl}
                style={{ width: '100%', height: 100, backgroundColor: '#f0f0f0' }}
                showDebugInfo={true}
              />
            </View>
          </View>
        </View>
      )}

      {/* 使用说明 */}
      <View style={{
        backgroundColor: '#e9ecef',
        padding: 15,
        borderRadius: 5
      }}>
        <ThemedText style={{ fontSize: 12, color: '#6c757d' }}>
          💡 使用说明：
        </ThemedText>
        <ThemedText style={{ fontSize: 11, color: '#6c757d', marginTop: 5 }}>
          • 输入图片URL进行认证测试{'\n'}
          • 对比普通图片组件和认证图片组件的加载效果{'\n'}
          • 如果看到401错误，说明图片需要认证{'\n'}
          • 使用AuthenticatedImage组件替代SafeImage来解决认证问题
        </ThemedText>
      </View>
    </ScrollView>
  );
};

export default ImageAuthFixer;
