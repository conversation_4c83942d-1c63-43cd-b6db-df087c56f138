// api/userProfile.js
import { API_BASE_URL } from '../constants/index';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchWithAuth } from './fetchWithAuth';
import { Platform } from 'react-native';

// 保存用户标签并生成 AI 画像
export async function saveUserTagAndGenerateProfile(selections) {
  try {
    // 将 selections 对象转换为 tagIdList 数组
    const tagIdList = Object.values(selections).flat();

    console.log(`[${Platform.OS}] saveUserTagAndGenerateProfile - selections:`, selections);
    console.log(`[${Platform.OS}] saveUserTagAndGenerateProfile - tagIdList:`, tagIdList);
    console.log(`[${Platform.OS}] 当前API_BASE_URL:`, API_BASE_URL);

    const requestBody = {
      tagIdList, // 使用正确的字段名 tagIdList
    };

    console.log(`[${Platform.OS}] saveUserTagAndGenerateProfile - requestBody:`, requestBody);

    const url = `${API_BASE_URL}/profile/save`;
    console.log(`[${Platform.OS}] 请求URL:`, url);

    const response = await fetchWithAuth(url, {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const result = await response.json();

    if (!response.ok || result.code !== 0) {
      throw new Error(result.message || '保存失败');
    }

    return result.data; // 即 UserProfileAiResponse
  } catch (error) {
    console.error(`[${Platform.OS}] API 错误:`, {
      message: error.message,
      stack: error.stack,
      selections,
      API_BASE_URL
    });

    // 提供更详细的错误信息
    if (error.message.includes('Network request failed') ||
        error.message.includes('fetch')) {
      console.error(`[${Platform.OS}] 网络连接失败，请检查：`);
      console.error('1. 后端服务是否在运行 (端口 8080)');
      console.error('2. IP地址配置是否正确');
      console.error('3. 手机和开发机器是否在同一网络');
      console.error('4. 防火墙是否阻止了连接');
    }

    throw error;
  }
}

// 发送选中id到后端
export async function sendSelectedIdsToBackend(ids) {
  try {
    console.log(`[${Platform.OS}] sendSelectedIdsToBackend - ids:`, ids);
    console.log(`[${Platform.OS}] 当前API_BASE_URL:`, API_BASE_URL);

    const requestBody = {
      tagIdList: ids // 使用正确的字段名 tagIdList
    };

    console.log(`[${Platform.OS}] sendSelectedIdsToBackend - requestBody:`, requestBody);

    const url = `${API_BASE_URL}/sav`;
    console.log(`[${Platform.OS}] 请求URL:`, url);

    // 使用fetchWithAuth添加token认证
    const response = await fetchWithAuth(url, {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`[${Platform.OS}] sendSelectedIdsToBackend 响应:`, result);

    return result;
  } catch (error) {
    console.error(`[${Platform.OS}] 发送选中ID到后端失败:`, {
      message: error.message,
      ids,
      API_BASE_URL
    });

    // 提供更详细的错误信息
    if (error.message.includes('Network request failed') ||
        error.message.includes('fetch')) {
      console.error(`[${Platform.OS}] 网络连接失败，请检查网络配置`);
    }

    // 可以根据需要决定是否抛出或静默
    throw error;
  }
}
