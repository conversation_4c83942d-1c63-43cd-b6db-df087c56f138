// api/userProfile.js
import { API_BASE_URL } from '../constants/index';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 保存用户标签并生成 AI 画像
export async function saveUserTagAndGenerateProfile(selections) {
  try {
    const token = await AsyncStorage.getItem('token'); // 假设你用 JWT 存在这里
    const response = await fetch(`${API_BASE_URL}/profile/save`, {

      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: JSON.stringify({
        selections, // selections 对应 request 中的字段 SaveTagRequest
      }),
    });

    const result = await response.json();

    if (!response.ok || result.code !== 0) {
      throw new Error(result.message || '保存失败');
    }


    return result.data; // 即 UserProfileAiResponse
  } catch (error) {
    console.error('API 错误:', error);
    throw error;
  }
}

// 发送选中id到后端
export async function sendSelectedIdsToBackend(ids) {
  try {
    // 这里请替换为你的后端实际接口地址
    const response = await fetch('http://localhost:8080/api/sav', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ids }),
    });
    if (!response.ok) {
      throw new Error('网络请求失败');
    }
    return await response.json();
  } catch (error) {
    console.error('发送选中ID到后端失败:', error);
    // 可以根据需要决定是否抛出或静默
  }
}
