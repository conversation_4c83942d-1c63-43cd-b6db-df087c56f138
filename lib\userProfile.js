// 注意：此文件已被 lib/apiServices.js 替代
// 为了向后兼容，这里重新导出新API系统的函数
import apiServices from './apiServices';
import { Platform } from 'react-native';

// 向后兼容：重新导出新API系统的函数
export const saveUserTagAndGenerateProfile = apiServices.profile.saveUserTagAndGenerateProfile;

// 向后兼容：sendSelectedIdsToBackend 函数
export async function sendSelectedIdsToBackend(ids) {
  console.log(`[${Platform.OS}] sendSelectedIdsToBackend (兼容模式):`, ids);
  // 使用新的API系统
  return await apiServices.profile.saveUserTagAndGenerateProfile({ tagIdList: ids });
}
