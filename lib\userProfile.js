// api/userProfile.js
import { API_BASE_URL } from '../constants/index';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchWithAuth } from './fetchWithAuth';

// 保存用户标签并生成 AI 画像
export async function saveUserTagAndGenerateProfile(selections) {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/profile/save`, {
      method: 'POST',
      body: JSON.stringify({
        selections, // selections 对应 request 中的字段 SaveTagRequest
      }),
    });

    const result = await response.json();

    if (!response.ok || result.code !== 0) {
      throw new Error(result.message || '保存失败');
    }

    return result.data; // 即 UserProfileAiResponse
  } catch (error) {
    console.error('API 错误:', error);
    throw error;
  }
}

// 发送选中id到后端
export async function sendSelectedIdsToBackend(ids) {
  try {
    // 使用fetchWithAuth添加token认证
    const response = await fetchWithAuth(`${API_BASE_URL}/sav`, {
      method: 'POST',
      body: JSON.stringify({ ids }),
    });

    if (!response.ok) {
      throw new Error('网络请求失败');
    }

    return await response.json();
  } catch (error) {
    console.error('发送选中ID到后端失败:', error);
    // 可以根据需要决定是否抛出或静默
  }
}
